import '../models/produit.dart';
import '../services/firebase_service.dart';

class ProduitService {
  // Créer un nouveau produit
  Future<String> creerProduit(Produit produit) async {
    try {
      final docRef = await FirebaseService.produits.add(produit.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('Erreur lors de la création du produit: $e');
    }
  }

  // Obtenir tous les produits
  Future<List<Produit>> obtenirTousLesProduits() async {
    try {
      final snapshot = await FirebaseService.produits.orderBy('nom').get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Produit.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des produits: $e');
    }
  }

  // Obtenir un produit par ID
  Future<Produit?> obtenirProduitParId(String id) async {
    try {
      final doc = await FirebaseService.produits.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Produit.fromMap({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Erreur lors de la récupération du produit: $e');
    }
  }

  // Rechercher des produits
  Future<List<Produit>> rechercherProduits(String query) async {
    try {
      final snapshot = await FirebaseService.produits.get();
      final produits =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Produit.fromMap({...data, 'id': doc.id});
          }).toList();

      return produits.where((produit) {
        final searchQuery = query.toLowerCase();
        return produit.nom.toLowerCase().contains(searchQuery) ||
            produit.description.toLowerCase().contains(searchQuery) ||
            produit.code.toLowerCase().contains(searchQuery);
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la recherche des produits: $e');
    }
  }

  // Mettre à jour un produit
  Future<void> mettreAJourProduit(Produit produit) async {
    try {
      await FirebaseService.produits.doc(produit.id).update(produit.toMap());
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du produit: $e');
    }
  }

  // Supprimer un produit
  Future<void> supprimerProduit(String id) async {
    try {
      await FirebaseService.produits.doc(id).delete();
    } catch (e) {
      throw Exception('Erreur lors de la suppression du produit: $e');
    }
  }

  // Obtenir les produits par catégorie
  Future<List<Produit>> obtenirProduitsParCategorie(String categorie) async {
    try {
      final snapshot =
          await FirebaseService.produits
              .where('categorie', isEqualTo: categorie)
              .orderBy('nom')
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Produit.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des produits par catégorie: $e',
      );
    }
  }

  // Obtenir les produits en rupture de stock
  Future<List<Produit>> obtenirProduitsEnRupture() async {
    try {
      final snapshot =
          await FirebaseService.produits
              .where('stock', isLessThanOrEqualTo: 0)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Produit.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des produits en rupture: $e',
      );
    }
  }

  // Obtenir les statistiques des produits
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      final snapshot = await FirebaseService.produits.get();
      final produits =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Produit.fromMap({...data, 'id': doc.id});
          }).toList();

      final total = produits.length;
      final enRupture = produits.where((p) => p.stock <= 0).length;
      final stockFaible =
          produits.where((p) => p.stock > 0 && p.stock <= 10).length;

      return {
        'total': total,
        'enRupture': enRupture,
        'stockFaible': stockFaible,
      };
    } catch (e) {
      throw Exception('Erreur lors de la récupération des statistiques: $e');
    }
  }

  // Obtenir les produits actifs
  Future<List<Produit>> obtenirProduitsActifs() async {
    try {
      final snapshot =
          await FirebaseService.produits
              .where('actif', isEqualTo: true)
              .orderBy('nom')
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Produit.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des produits actifs: $e');
    }
  }

  // Obtenir les catégories de produits
  Future<List<String>> obtenirCategories() async {
    try {
      final snapshot = await FirebaseService.produits.get();
      final categories = <String>{};

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        if (data['categorie'] != null) {
          categories.add(data['categorie'].toString());
        }
      }

      return categories.toList()..sort();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des catégories: $e');
    }
  }

  // Obtenir un produit par code
  Future<Produit?> obtenirProduitParCode(String code) async {
    try {
      final snapshot =
          await FirebaseService.produits
              .where('code', isEqualTo: code)
              .limit(1)
              .get();

      if (snapshot.docs.isNotEmpty) {
        final doc = snapshot.docs.first;
        final data = doc.data() as Map<String, dynamic>;
        return Produit.fromMap({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Erreur lors de la récupération du produit par code: $e');
    }
  }

  // Mettre à jour le stock d'un produit
  Future<void> mettreAJourStock(String id, int nouveauStock) async {
    try {
      await FirebaseService.produits.doc(id).update({'stock': nouveauStock});
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du stock: $e');
    }
  }

  // Obtenir les produits en rupture de stock (alias)
  Future<List<Produit>> obtenirProduitsRuptureStock() async {
    return obtenirProduitsEnRupture();
  }

  // Obtenir les produits avec stock faible
  Future<List<Produit>> obtenirProduitsStockFaible() async {
    try {
      final snapshot =
          await FirebaseService.produits
              .where('stock', isGreaterThan: 0)
              .where('stock', isLessThanOrEqualTo: 10)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Produit.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des produits à stock faible: $e',
      );
    }
  }

  // Obtenir le nombre total de produits
  Future<int> obtenirNombreProduits() async {
    try {
      final snapshot = await FirebaseService.produits.get();
      return snapshot.docs.length;
    } catch (e) {
      throw Exception('Erreur lors du comptage des produits: $e');
    }
  }
}
