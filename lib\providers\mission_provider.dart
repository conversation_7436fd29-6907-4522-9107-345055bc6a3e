import 'package:flutter/material.dart';
import '../models/mission.dart';
import '../services/mission_service.dart';

class MissionProvider with ChangeNotifier {
  final MissionService _missionService = MissionService();

  List<Mission> _missions = [];
  List<Mission> _missionsDuJour = [];
  List<Mission> _missionsEnRetard = [];
  bool _isLoading = false;
  String? _error;

  List<Mission> get missions => _missions;
  List<Mission> get missionsDuJour => _missionsDuJour;
  List<Mission> get missionsEnRetard => _missionsEnRetard;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Charger toutes les missions
  Future<void> chargerMissions() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _missions = await _missionService.obtenirToutesLesMissions();
    } catch (e) {
      _error = 'Erreur lors du chargement des missions: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger les missions d'un merchandiser
  Future<void> chargerMissionsParMerchandiser(String merchandiserId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _missions = await _missionService.obtenirMissionsParMerchandiser(
        merchandiserId,
      );
    } catch (e) {
      _error = 'Erreur lors du chargement des missions: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger les missions créées par un commercial
  Future<void> chargerMissionsParCommercial(String commercialId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _missions = await _missionService.obtenirMissionsParCommercial(
        commercialId,
      );
    } catch (e) {
      _error = 'Erreur lors du chargement des missions: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger les missions du jour
  Future<void> chargerMissionsDuJour(String merchandiserId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _missionsDuJour = await _missionService.obtenirMissionsDuJour(
        merchandiserId,
      );
    } catch (e) {
      _error = 'Erreur lors du chargement des missions du jour: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger les missions en retard
  Future<void> chargerMissionsEnRetard(String merchandiserId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _missionsEnRetard = await _missionService.obtenirMissionsEnRetard(
        merchandiserId,
      );
    } catch (e) {
      _error = 'Erreur lors du chargement des missions en retard: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Créer une nouvelle mission
  Future<bool> creerMission(Mission mission) async {
    try {
      await _missionService.creerMission(mission);
      // Recharger les missions après création
      await chargerMissions();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création de la mission: $e';
      notifyListeners();
      return false;
    }
  }

  // Mettre à jour le statut d'une mission
  Future<bool> mettreAJourStatut(String missionId, String nouveauStatut) async {
    try {
      await _missionService.mettreAJourStatut(missionId, nouveauStatut);

      // Mettre à jour localement
      final index = _missions.indexWhere((m) => m.id == missionId);
      if (index != -1) {
        _missions[index] = _missions[index].copyWith(statut: nouveauStatut);
      }

      // Mettre à jour dans les missions du jour
      final indexJour = _missionsDuJour.indexWhere((m) => m.id == missionId);
      if (indexJour != -1) {
        _missionsDuJour[indexJour] = _missionsDuJour[indexJour].copyWith(
          statut: nouveauStatut,
        );
      }

      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour du statut: $e';
      notifyListeners();
      return false;
    }
  }

  // Mettre à jour une mission
  Future<bool> mettreAJourMission(Mission mission) async {
    try {
      await _missionService.mettreAJourMission(mission);

      // Mettre à jour localement
      final index = _missions.indexWhere((m) => m.id == mission.id);
      if (index != -1) {
        _missions[index] = mission;
      }

      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour de la mission: $e';
      notifyListeners();
      return false;
    }
  }

  // Supprimer une mission
  Future<bool> supprimerMission(String missionId) async {
    try {
      await _missionService.supprimerMission(missionId);

      // Supprimer localement
      _missions.removeWhere((m) => m.id == missionId);
      _missionsDuJour.removeWhere((m) => m.id == missionId);
      _missionsEnRetard.removeWhere((m) => m.id == missionId);

      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la suppression de la mission: $e';
      notifyListeners();
      return false;
    }
  }

  // Obtenir une mission par ID
  Future<Mission?> obtenirMissionParId(String missionId) async {
    try {
      return await _missionService.obtenirMissionParId(missionId);
    } catch (e) {
      _error = 'Erreur lors de la récupération de la mission: $e';
      notifyListeners();
      return null;
    }
  }

  // Obtenir les missions par statut
  Future<List<Mission>> obtenirMissionsParStatut(
    String merchandiserId,
    String statut,
  ) async {
    try {
      return await _missionService.obtenirMissionsParStatut(
        merchandiserId,
        statut,
      );
    } catch (e) {
      _error = 'Erreur lors de la récupération des missions par statut: $e';
      notifyListeners();
      return [];
    }
  }

  // Filtrer les missions par statut localement
  List<Mission> filtrerParStatut(String statut) {
    return _missions.where((mission) => mission.statut == statut).toList();
  }

  // Filtrer les missions par priorité localement
  List<Mission> filtrerParPriorite(String priorite) {
    return _missions.where((mission) => mission.priorite == priorite).toList();
  }

  // Obtenir les missions urgentes
  List<Mission> get missionsUrgentes {
    return _missions.where((mission) => mission.estUrgente).toList();
  }

  // Obtenir les statistiques des missions
  Map<String, int> get statistiquesMissions {
    final stats = <String, int>{};
    for (final mission in _missions) {
      stats[mission.statut] = (stats[mission.statut] ?? 0) + 1;
    }
    return stats;
  }

  // Générer un ID unique pour une mission
  String genererIdMission() {
    return _missionService.genererIdMission();
  }

  // Effacer l'erreur
  void effacerErreur() {
    _error = null;
    notifyListeners();
  }
}
