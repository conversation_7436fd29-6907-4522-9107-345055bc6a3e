import '../models/devis.dart';
import '../services/firebase_service.dart';

class DevisService {
  // Créer un nouveau devis
  Future<String> creerDevis(Devis devis) async {
    try {
      final docRef = await FirebaseService.devis.add(devis.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('Erreur lors de la création du devis: $e');
    }
  }

  // Obtenir tous les devis
  Future<List<Devis>> obtenirTousLesDevis() async {
    try {
      final snapshot =
          await FirebaseService.devis
              .orderBy('dateDevis', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Devis.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des devis: $e');
    }
  }

  // Obtenir un devis par ID
  Future<Devis?> obtenirDevisParId(String id) async {
    try {
      final doc = await FirebaseService.devis.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Devis.fromMap({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Erreur lors de la récupération du devis: $e');
    }
  }

  // Obtenir les devis d'un client
  Future<List<Devis>> obtenirDevisParClient(String clientId) async {
    try {
      final snapshot =
          await FirebaseService.devis
              .where('clientId', isEqualTo: clientId)
              .orderBy('dateDevis', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Devis.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des devis du client: $e');
    }
  }

  // Mettre à jour le statut d'un devis
  Future<void> mettreAJourStatut(String devisId, String nouveauStatut) async {
    try {
      await FirebaseService.devis.doc(devisId).update({
        'statut': nouveauStatut,
      });
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du statut: $e');
    }
  }

  // Obtenir les devis par statut
  Future<List<Devis>> obtenirDevisParStatut(String statut) async {
    try {
      final snapshot =
          await FirebaseService.devis
              .where('statut', isEqualTo: statut)
              .orderBy('dateDevis', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Devis.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des devis par statut: $e',
      );
    }
  }

  // Compter le nombre total de devis
  Future<int> obtenirNombreDevis() async {
    try {
      final snapshot = await FirebaseService.devis.get();
      return snapshot.docs.length;
    } catch (e) {
      throw Exception('Erreur lors du comptage des devis: $e');
    }
  }

  // Obtenir les statistiques des devis
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      final snapshot = await FirebaseService.devis.get();
      final devisList =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Devis.fromMap({...data, 'id': doc.id});
          }).toList();

      final total = devisList.length;
      final enAttente = devisList.where((d) => d.statut == 'en_attente').length;
      final acceptes = devisList.where((d) => d.statut == 'accepte').length;
      final refuses = devisList.where((d) => d.statut == 'refuse').length;

      return {
        'total': total,
        'enAttente': enAttente,
        'acceptes': acceptes,
        'refuses': refuses,
      };
    } catch (e) {
      throw Exception('Erreur lors de la récupération des statistiques: $e');
    }
  }

  // Supprimer un devis
  Future<void> supprimerDevis(String id) async {
    try {
      await FirebaseService.devis.doc(id).delete();
    } catch (e) {
      throw Exception('Erreur lors de la suppression du devis: $e');
    }
  }

  // Dupliquer un devis
  Future<String> dupliquerDevis(String id) async {
    try {
      final devis = await obtenirDevisParId(id);
      if (devis == null) {
        throw Exception('Devis non trouvé');
      }

      final nouveauDevis = devis.copyWith(
        id: null,
        numero: await genererNumeroDevis(),
        dateCreation: DateTime.now(),
        statut: StatutDevis.brouillon,
      );

      return await creerDevis(nouveauDevis);
    } catch (e) {
      throw Exception('Erreur lors de la duplication du devis: $e');
    }
  }

  // Transformer un devis en commande
  Future<String> transformerEnCommande(String devisId) async {
    try {
      final devis = await obtenirDevisParId(devisId);
      if (devis == null) {
        throw Exception('Devis non trouvé');
      }

      if (devis.statut != StatutDevis.accepte) {
        throw Exception(
          'Seuls les devis acceptés peuvent être transformés en commande',
        );
      }

      // Créer la commande à partir du devis
      // Cette méthode devrait être implémentée selon votre logique métier
      await mettreAJourStatut(devisId, StatutDevis.accepte.name);

      return 'commande-from-devis-$devisId';
    } catch (e) {
      throw Exception('Erreur lors de la transformation en commande: $e');
    }
  }

  // Générer un numéro de devis unique
  Future<String> genererNumeroDevis() async {
    try {
      final now = DateTime.now();
      final year = now.year.toString().substring(2);
      final month = now.month.toString().padLeft(2, '0');

      // Obtenir le dernier numéro du mois
      final prefix = 'DEV$year$month';
      final snapshot =
          await FirebaseService.devis
              .where('numero', isGreaterThanOrEqualTo: prefix)
              .where('numero', isLessThan: '${prefix}Z')
              .orderBy('numero', descending: true)
              .limit(1)
              .get();

      int numero = 1;
      if (snapshot.docs.isNotEmpty) {
        final data = snapshot.docs.first.data() as Map<String, dynamic>;
        final lastNumero = data['numero'] as String;
        final lastNumber =
            int.tryParse(lastNumero.substring(prefix.length)) ?? 0;
        numero = lastNumber + 1;
      }

      return '$prefix${numero.toString().padLeft(3, '0')}';
    } catch (e) {
      throw Exception('Erreur lors de la génération du numéro de devis: $e');
    }
  }
}
