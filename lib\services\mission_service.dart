import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/mission.dart';

class MissionService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'missions';

  // Créer une nouvelle mission
  Future<void> creerMission(Mission mission) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(mission.id)
          .set(mission.toMap());
    } catch (e) {
      throw Exception('Erreur lors de la création de la mission: $e');
    }
  }

  // Obtenir toutes les missions
  Future<List<Mission>> obtenirToutesLesMissions() async {
    try {
      QuerySnapshot snapshot = await _firestore.collection(_collection).get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Mission.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des missions: $e');
    }
  }

  // Obtenir les missions d'un merchandiser
  Future<List<Mission>> obtenirMissionsParMerchandiser(
    String merchandiserId,
  ) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('merchandiserId', isEqualTo: merchandiserId)
              .orderBy('dateEcheance', descending: false)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Mission.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des missions du merchandiser: $e',
      );
    }
  }

  // Obtenir les missions créées par un commercial
  Future<List<Mission>> obtenirMissionsParCommercial(
    String commercialId,
  ) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('commercialId', isEqualTo: commercialId)
              .orderBy('dateCreation', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Mission.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des missions du commercial: $e',
      );
    }
  }

  // Obtenir les missions du jour pour un merchandiser
  Future<List<Mission>> obtenirMissionsDuJour(String merchandiserId) async {
    try {
      DateTime aujourdhui = DateTime.now();
      DateTime debutJour = DateTime(
        aujourdhui.year,
        aujourdhui.month,
        aujourdhui.day,
      );
      DateTime finJour = debutJour.add(Duration(days: 1));

      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('merchandiserId', isEqualTo: merchandiserId)
              .where('dateEcheance', isGreaterThanOrEqualTo: debutJour)
              .where('dateEcheance', isLessThan: finJour)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Mission.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des missions du jour: $e',
      );
    }
  }

  // Obtenir une mission par ID
  Future<Mission?> obtenirMissionParId(String missionId) async {
    try {
      DocumentSnapshot doc =
          await _firestore.collection(_collection).doc(missionId).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Mission.fromMap({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Erreur lors de la récupération de la mission: $e');
    }
  }

  // Mettre à jour le statut d'une mission
  Future<void> mettreAJourStatut(String missionId, String nouveauStatut) async {
    try {
      await _firestore.collection(_collection).doc(missionId).update({
        'statut': nouveauStatut,
      });
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du statut: $e');
    }
  }

  // Mettre à jour une mission
  Future<void> mettreAJourMission(Mission mission) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(mission.id)
          .update(mission.toMap());
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour de la mission: $e');
    }
  }

  // Supprimer une mission
  Future<void> supprimerMission(String missionId) async {
    try {
      await _firestore.collection(_collection).doc(missionId).delete();
    } catch (e) {
      throw Exception('Erreur lors de la suppression de la mission: $e');
    }
  }

  // Obtenir les missions en retard
  Future<List<Mission>> obtenirMissionsEnRetard(String merchandiserId) async {
    try {
      DateTime maintenant = DateTime.now();
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('merchandiserId', isEqualTo: merchandiserId)
              .where('dateEcheance', isLessThan: maintenant)
              .where('statut', isNotEqualTo: 'terminee')
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Mission.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des missions en retard: $e',
      );
    }
  }

  // Obtenir les missions par statut
  Future<List<Mission>> obtenirMissionsParStatut(
    String merchandiserId,
    String statut,
  ) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('merchandiserId', isEqualTo: merchandiserId)
              .where('statut', isEqualTo: statut)
              .orderBy('dateEcheance', descending: false)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Mission.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des missions par statut: $e',
      );
    }
  }

  // Générer un ID unique pour une mission
  String genererIdMission() {
    return _firestore.collection(_collection).doc().id;
  }

  // Écouter les changements des missions d'un merchandiser
  Stream<List<Mission>> ecouterMissionsParMerchandiser(String merchandiserId) {
    return _firestore
        .collection(_collection)
        .where('merchandiserId', isEqualTo: merchandiserId)
        .orderBy('dateEcheance', descending: false)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) {
                final data = doc.data() as Map<String, dynamic>;
                return Mission.fromMap({...data, 'id': doc.id});
              }).toList(),
        );
  }
}
