import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/devis.dart';
import '../../models/client.dart';
import '../../providers/firebase_client_provider.dart';
import '../../services/whatsapp_service.dart';
import '../../widgets/whatsapp_message_dialog.dart';

class DevisDetailScreen extends StatefulWidget {
  final Devis devis;

  const DevisDetailScreen({super.key, required this.devis});

  @override
  State<DevisDetailScreen> createState() => _DevisDetailScreenState();
}

class _DevisDetailScreenState extends State<DevisDetailScreen> {
  Client? client;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerClient();
    });
  }

  void _chargerClient() async {
    final clientProvider = context.read<FirebaseClientProvider>();
    if (!clientProvider.isLoaded) {
      await Future.microtask(() async {
        await clientProvider.chargerClients();
      });
    }

    final clients = clientProvider.clients;
    setState(() {
      client = clients.firstWhere(
        (c) => c.id == widget.devis.clientId,
        orElse:
            () => Client(
              nom: 'Client inconnu',
              prenom: '',
              email: '',
              telephone: '',
              adresse: '',
              dateCreation: DateTime.now(),
            ),
      );
    });
  }

  void _envoyerParWhatsApp() async {
    if (client == null || client!.telephone.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Numéro de téléphone du client manquant'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // Générer l'aperçu du message
      final messageParDefaut =
          WhatsAppService.genererMessageDevis(
            widget.devis,
            client!,
            null,
          ).split('\n').take(3).join('\n') +
          '...'; // Afficher la boîte de dialogue pour personnaliser le message
      final messagePersonnalise = await showDialog<String>(
        context: context,
        builder:
            (context) => WhatsAppMessageDialog(
              titre: 'Envoyer le devis par WhatsApp',
              messageParDefaut: messageParDefaut,
              client: client!,
              onEnvoyer: () {},
            ),
      );

      // Vérifier si l'utilisateur n'a pas annulé
      if (messagePersonnalise == null) {
        return; // L'utilisateur a cliqué sur Annuler
      }

      // Envoyer le devis (chaîne vide = message par défaut, sinon message personnalisé)
      final messageAUtiliser =
          messagePersonnalise.isEmpty ? null : messagePersonnalise;
      final success = await WhatsAppService.envoyerDevis(
        devis: widget.devis,
        client: client!,
        messagePersonnalise: messageAUtiliser,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'WhatsApp ouvert avec le devis'
                  : 'Erreur lors de l\'ouverture de WhatsApp',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Scaffold(
      appBar: AppBar(
        title: Text('Devis ${widget.devis.numeroFormate}'),
        actions: [
          IconButton(
            icon: Icon(
              Icons.chat,
              color:
                  client?.telephone.isNotEmpty == true
                      ? const Color(0xFF25D366)
                      : Colors.grey,
            ),
            onPressed:
                client?.telephone.isNotEmpty == true
                    ? () => _envoyerParWhatsApp()
                    : null,
            tooltip: 'Envoyer par WhatsApp',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // TODO: Implémenter le partage
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Partage en cours de développement'),
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec statut
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          widget.devis.numeroFormate,
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        _buildStatutChip(widget.devis.statut),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Date de création',
                                style: theme.textTheme.labelMedium,
                              ),
                              Text(
                                dateFormat.format(widget.devis.dateCreation),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Date d\'expiration',
                                style: theme.textTheme.labelMedium,
                              ),
                              Text(
                                dateFormat.format(widget.devis.dateExpiration),
                                style: TextStyle(
                                  color:
                                      widget.devis.estExpire
                                          ? Colors.red
                                          : null,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Informations client
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Client',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (client != null) ...[
                      Text('${client!.prenom} ${client!.nom}'),
                      if (client!.email.isNotEmpty) Text(client!.email),
                      if (client!.telephone.isNotEmpty) Text(client!.telephone),
                    ] else
                      const Text('Chargement...'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Articles
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Articles',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...widget.devis.items.map(
                      (item) => Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 3,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item.designation,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    'Ref: ${item.reference}',
                                    style: theme.textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Text(
                                '${item.quantite} ${item.unite}',
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                item.prixUnitaireFormate,
                                textAlign: TextAlign.right,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                item.sousTotalFormate,
                                textAlign: TextAlign.right,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Totaux
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildLigneTotaux(
                      'Sous-total HT',
                      widget.devis.sousTotalFormate,
                    ),
                    if (widget.devis.montantRemise > 0)
                      _buildLigneTotaux(
                        'Remise',
                        '- ${widget.devis.montantRemiseFormate}',
                      ),
                    _buildLigneTotaux('Total HT', widget.devis.totalHTFormate),
                    _buildLigneTotaux(
                      'TVA (${widget.devis.tauxTva}%)',
                      widget.devis.montantTvaFormate,
                    ),
                    const Divider(),
                    _buildLigneTotaux(
                      'Total TTC',
                      widget.devis.totalTTCFormate,
                      isTotal: true,
                    ),
                  ],
                ),
              ),
            ),

            // Notes
            if (widget.devis.notes?.isNotEmpty == true) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notes',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(widget.devis.notes!),
                    ],
                  ),
                ),
              ),
            ],

            // Conditions de validité
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Conditions de validité',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(widget.devis.conditionsValidite),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatutChip(StatutDevis statut) {
    Color couleur;
    switch (statut) {
      case StatutDevis.brouillon:
        couleur = Colors.grey;
        break;
      case StatutDevis.envoye:
        couleur = Colors.blue;
        break;
      case StatutDevis.accepte:
        couleur = Colors.green;
        break;
      case StatutDevis.refuse:
        couleur = Colors.red;
        break;
      case StatutDevis.expire:
        couleur = Colors.orange;
        break;
    }

    return Chip(
      label: Text(
        statut.name.toUpperCase(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: couleur,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildLigneTotaux(
    String label,
    String montant, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            montant,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
        ],
      ),
    );
  }
}
