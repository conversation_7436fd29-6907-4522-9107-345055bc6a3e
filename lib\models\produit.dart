class Produit {
  final String? id;
  final String code;
  final String nom;
  final String description;
  final double prix;
  final int stock;
  final String? imageUrl;
  final String categorie;
  final DateTime dateCreation;
  final bool actif;

  Produit({
    this.id,
    required this.code,
    required this.nom,
    required this.description,
    required this.prix,
    required this.stock,
    this.imageUrl,
    required this.categorie,
    required this.dateCreation,
    this.actif = true,
  });

  // Convertir un Produit en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'nom': nom,
      'description': description,
      'prix': prix,
      'stock': stock,
      'imageUrl': imageUrl,
      'categorie': categorie,
      'dateCreation': dateCreation.toIso8601String(),
      'actif': actif ? 1 : 0,
    };
  }

  // Créer un Produit à partir d'une Map de la base de données
  factory Produit.fromMap(Map<String, dynamic> map) {
    return Produit(
      id: map['id'],
      code: map['code'],
      nom: map['nom'],
      description: map['description'],
      prix: map['prix'].toDouble(),
      stock: map['stock'],
      imageUrl: map['imageUrl'],
      categorie: map['categorie'],
      dateCreation: DateTime.parse(map['dateCreation']),
      actif: map['actif'] == 1,
    );
  }

  // Créer une copie du produit avec des modifications
  Produit copyWith({
    String? id,
    String? code,
    String? nom,
    String? description,
    double? prix,
    int? stock,
    String? imageUrl,
    String? categorie,
    DateTime? dateCreation,
    bool? actif,
  }) {
    return Produit(
      id: id ?? this.id,
      code: code ?? this.code,
      nom: nom ?? this.nom,
      description: description ?? this.description,
      prix: prix ?? this.prix,
      stock: stock ?? this.stock,
      imageUrl: imageUrl ?? this.imageUrl,
      categorie: categorie ?? this.categorie,
      dateCreation: dateCreation ?? this.dateCreation,
      actif: actif ?? this.actif,
    );
  }

  // Vérifier si le produit est disponible
  bool get estDisponible => actif && stock > 0;

  // Prix formaté
  String get prixFormate => '${prix.toStringAsFixed(2)} €';

  @override
  String toString() {
    return 'Produit{id: $id, nom: $nom, prix: $prix, stock: $stock}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Produit && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
