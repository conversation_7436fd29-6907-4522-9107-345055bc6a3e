import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    await FirebaseService.initializeFirestore();

    runApp(MyApp());
  } catch (e) {
    print('Erreur lors de l\'initialisation Firebase: $e');
  }
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VitaBrosse Pro',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: TestFirebaseScreen(),
    );
  }
}

class TestFirebaseScreen extends StatefulWidget {
  @override
  _TestFirebaseScreenState createState() => _TestFirebaseScreenState();
}

class _TestFirebaseScreenState extends State<TestFirebaseScreen> {
  String _status = 'Initialisation...';

  @override
  void initState() {
    super.initState();
    _testFirebaseConnection();
  }

  Future<void> _testFirebaseConnection() async {
    try {
      setState(() {
        _status = 'Test de connexion Firebase en cours...';
      });

      // Test d'écriture
      await FirebaseService.clients.add({
        'nom': 'Test Client',
        'prenom': 'Test',
        'email': '<EMAIL>',
        'telephone': '12345678',
        'adresse': 'Test Address',
        'dateCreation': DateTime.now(),
      });

      // Test de lecture
      final snapshot = await FirebaseService.clients.get();
      final count = snapshot.docs.length;

      setState(() {
        _status =
            '✅ Firebase connecté avec succès!\\n$count clients dans la base de données';
      });
    } catch (e) {
      setState(() {
        _status = '❌ Erreur Firebase: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test Firebase'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.cloud, size: 100, color: Colors.blue),
              SizedBox(height: 20),
              Text(
                'VitaBrosse Pro',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Text(
                _status,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 30),
              ElevatedButton(
                onPressed: _testFirebaseConnection,
                child: Text('Tester à nouveau'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
