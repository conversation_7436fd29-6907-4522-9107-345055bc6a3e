@echo off
echo ========================================
echo Installation Firebase pour VitaBrosse Pro
echo ========================================

echo.
echo Etape 1: Installation des outils Firebase...
echo.

REM Vérifier si Node.js est installé
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Node.js n'est pas installé. Veuillez installer Node.js d'abord.
    pause
    exit /b 1
)

REM Installer Firebase CLI
echo Installation de Firebase CLI...
npm install -g firebase-tools
if %errorlevel% neq 0 (
    echo ERREUR: Échec de l'installation de Firebase CLI
    pause
    exit /b 1
)

echo.
echo Etape 2: Installation de FlutterFire CLI...
echo.

REM Installer FlutterFire CLI
dart pub global activate flutterfire_cli
if %errorlevel% neq 0 (
    echo ERREUR: Échec de l'installation de FlutterFire CLI
    pause
    exit /b 1
)

echo.
echo Etape 3: Installation des dépendances Flutter...
echo.

REM Installer les dépendances
flutter pub get
if %errorlevel% neq 0 (
    echo ERREUR: Échec de l'installation des dépendances
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation terminée avec succès !
echo ========================================
echo.
echo Prochaines étapes:
echo 1. Créer un projet Firebase sur https://console.firebase.google.com/
echo 2. Exécuter: firebase login
echo 3. Exécuter: flutterfire configure
echo 4. Suivre les instructions dans docs/FIREBASE_SETUP.md
echo.
pause
