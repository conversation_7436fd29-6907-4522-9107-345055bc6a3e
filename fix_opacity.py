#!/usr/bin/env python3
"""
Script pour remplacer automatiquement .withOpacity() par .withValues() 
dans tous les fichiers Dart du projet VitaBrosse Pro.
"""

import os
import re
import glob

def replace_with_opacity_in_file(file_path):
    """Remplace .withOpacity() par .withValues() dans un fichier."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Pattern pour capturer .withOpacity(value)
        pattern = r'\.withOpacity\(([^)]+)\)'
        
        def replacement(match):
            opacity_value = match.group(1)
            return f'.withValues(alpha: {opacity_value})'
        
        # Remplacer toutes les occurrences
        new_content = re.sub(pattern, replacement, content)
        
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(new_content)
            print(f"✅ Modifié: {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ Erreur avec {file_path}: {e}")
        return False

def main():
    """Fonction principale."""
    print("🔄 Remplacement de .withOpacity() par .withValues()...")
    
    # Chercher tous les fichiers .dart dans lib/
    dart_files = glob.glob("lib/**/*.dart", recursive=True)
    
    modified_files = 0
    total_files = len(dart_files)
    
    for file_path in dart_files:
        if replace_with_opacity_in_file(file_path):
            modified_files += 1
    
    print(f"\n📊 Résultats:")
    print(f"   - Fichiers traités: {total_files}")
    print(f"   - Fichiers modifiés: {modified_files}")
    print(f"   - Fichiers inchangés: {total_files - modified_files}")
    
    if modified_files > 0:
        print(f"\n✨ Correction terminée ! {modified_files} fichiers ont été mis à jour.")
        print("💡 Lancez 'flutter analyze' pour vérifier les résultats.")
    else:
        print("\n✅ Aucune modification nécessaire.")

if __name__ == "__main__":
    main()
