import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/tache_merchandising.dart';
import '../../providers/tache_merchandising_provider.dart';
import '../../providers/merchandiser_provider.dart';
import '../../providers/magasin_provider.dart';

class AjouterTacheScreen extends StatefulWidget {
  final TacheMerchandising? tache;
  final DateTime? dateInitiale;

  const AjouterTacheScreen({super.key, this.tache, this.dateInitiale});

  @override
  State<AjouterTacheScreen> createState() => _AjouterTacheScreenState();
}

class _AjouterTacheScreenState extends State<AjouterTacheScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titreController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _commentairesController = TextEditingController();

  TypeTache _selectedType = TypeTache.visite;
  PrioriteTache _selectedPriorite = PrioriteTache.normale;
  int? _selectedMerchandiserId;
  int? _selectedMagasinId;
  DateTime _selectedDate = DateTime.now();
  TimeOfDay? _selectedTime;
  Duration? _dureeEstimee;
  bool _rappel = false;
  DateTime? _dateRappel;

  @override
  void initState() {
    super.initState();
    if (widget.dateInitiale != null) {
      _selectedDate = widget.dateInitiale!;
    }
    if (widget.tache != null) {
      _initializeWithExistingTache();
    }
  }

  void _initializeWithExistingTache() {
    final tache = widget.tache!;
    _titreController.text = tache.titre;
    _descriptionController.text = tache.description;
    _commentairesController.text = tache.commentaires ?? '';
    _selectedType = tache.type;
    _selectedPriorite = tache.priorite;
    _selectedMerchandiserId = tache.merchandiserId;
    _selectedMagasinId = tache.magasinId;
    _selectedDate = tache.dateEcheance;
    if (tache.heureDebut != null) {
      _selectedTime = TimeOfDay.fromDateTime(tache.heureDebut!);
    }
    _dureeEstimee = tache.dureeEstimee;
    _rappel = tache.rappel;
    _dateRappel = tache.dateRappel;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.tache == null ? 'Ajouter une tâche' : 'Modifier la tâche',
        ),
        actions: [
          TextButton(onPressed: _sauvegarder, child: const Text('Enregistrer')),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Titre
            TextFormField(
              controller: _titreController,
              decoration: const InputDecoration(
                labelText: 'Titre de la tâche',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Le titre est requis';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Description
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'La description est requise';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Type
            DropdownButtonFormField<TypeTache>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Type de tâche',
                border: OutlineInputBorder(),
              ),
              items:
                  TypeTache.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(_getTypeLabel(type)),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
              },
            ),
            const SizedBox(height: 16),

            // Priorité
            DropdownButtonFormField<PrioriteTache>(
              value: _selectedPriorite,
              decoration: const InputDecoration(
                labelText: 'Priorité',
                border: OutlineInputBorder(),
              ),
              items:
                  PrioriteTache.values.map((priorite) {
                    return DropdownMenuItem(
                      value: priorite,
                      child: Text(_getPrioriteLabel(priorite)),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPriorite = value!;
                });
              },
            ),
            const SizedBox(height: 16),

            // Merchandiser
            Consumer<MerchandiserProvider>(
              builder: (context, provider, child) {
                return DropdownButtonFormField<int>(
                  value: _selectedMerchandiserId,
                  decoration: const InputDecoration(
                    labelText: 'Merchandiser',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      provider.merchandisers.map((merchandiser) {
                        return DropdownMenuItem(
                          value: merchandiser.id,
                          child: Text(
                            '${merchandiser.nom} ${merchandiser.prenom}',
                          ),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedMerchandiserId = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Veuillez sélectionner un merchandiser';
                    }
                    return null;
                  },
                );
              },
            ),
            const SizedBox(height: 16),

            // Magasin (optionnel)
            Consumer<MagasinProvider>(
              builder: (context, provider, child) {
                return DropdownButtonFormField<int>(
                  value: _selectedMagasinId,
                  decoration: const InputDecoration(
                    labelText: 'Magasin (optionnel)',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem<int>(
                      value: null,
                      child: Text('Aucun magasin'),
                    ),
                    ...provider.magasins.map((magasin) {
                      return DropdownMenuItem(
                        value: magasin.id,
                        child: Text(magasin.nom),
                      );
                    }).toList(),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedMagasinId = value;
                    });
                  },
                );
              },
            ),
            const SizedBox(height: 16),

            // Date d'échéance
            ListTile(
              title: const Text('Date d\'échéance'),
              subtitle: Text(
                '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
              ),
              trailing: const Icon(Icons.calendar_today),
              onTap: _selectDate,
            ),
            const SizedBox(height: 16),

            // Heure (optionnel)
            ListTile(
              title: const Text('Heure (optionnel)'),
              subtitle: Text(_selectedTime?.format(context) ?? 'Aucune heure'),
              trailing: const Icon(Icons.access_time),
              onTap: _selectTime,
            ),
            const SizedBox(height: 16),

            // Durée estimée
            ListTile(
              title: const Text('Durée estimée (optionnel)'),
              subtitle: Text(
                _dureeEstimee != null
                    ? '${_dureeEstimee!.inHours}h ${_dureeEstimee!.inMinutes % 60}min'
                    : 'Aucune durée',
              ),
              trailing: const Icon(Icons.timer),
              onTap: _selectDuree,
            ),
            const SizedBox(height: 16),

            // Rappel
            SwitchListTile(
              title: const Text('Rappel'),
              subtitle: const Text('Activer les notifications'),
              value: _rappel,
              onChanged: (value) {
                setState(() {
                  _rappel = value;
                  if (!value) {
                    _dateRappel = null;
                  }
                });
              },
            ),

            // Date de rappel
            if (_rappel)
              ListTile(
                title: const Text('Date de rappel'),
                subtitle: Text(
                  _dateRappel != null
                      ? '${_dateRappel!.day}/${_dateRappel!.month}/${_dateRappel!.year}'
                      : 'Sélectionner une date',
                ),
                trailing: const Icon(Icons.notification_important),
                onTap: _selectDateRappel,
              ),
            const SizedBox(height: 16),

            // Commentaires
            TextFormField(
              controller: _commentairesController,
              decoration: const InputDecoration(
                labelText: 'Commentaires (optionnel)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
    );
    if (time != null) {
      setState(() {
        _selectedTime = time;
      });
    }
  }

  Future<void> _selectDuree() async {
    final result = await showDialog<Duration>(
      context: context,
      builder: (context) => _DureeDialog(dureeInitiale: _dureeEstimee),
    );
    if (result != null) {
      setState(() {
        _dureeEstimee = result;
      });
    }
  }

  Future<void> _selectDateRappel() async {
    final date = await showDatePicker(
      context: context,
      initialDate:
          _dateRappel ?? _selectedDate.subtract(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: _selectedDate,
    );
    if (date != null) {
      setState(() {
        _dateRappel = date;
      });
    }
  }

  void _sauvegarder() async {
    if (_formKey.currentState!.validate()) {
      final provider = Provider.of<TacheMerchandisingProvider>(
        context,
        listen: false,
      );

      DateTime? heureDebut;
      if (_selectedTime != null) {
        heureDebut = DateTime(
          _selectedDate.year,
          _selectedDate.month,
          _selectedDate.day,
          _selectedTime!.hour,
          _selectedTime!.minute,
        );
      }

      final tache = TacheMerchandising(
        id: widget.tache?.id,
        titre: _titreController.text,
        description: _descriptionController.text,
        type: _selectedType,
        priorite: _selectedPriorite,
        merchandiserId: _selectedMerchandiserId!,
        magasinId: _selectedMagasinId,
        dateEcheance: _selectedDate,
        heureDebut: heureDebut,
        dureeEstimee: _dureeEstimee,
        commentaires:
            _commentairesController.text.isNotEmpty
                ? _commentairesController.text
                : null,
        dateCreation: widget.tache?.dateCreation ?? DateTime.now(),
        rappel: _rappel,
        dateRappel: _dateRappel,
      );

      bool success;
      if (widget.tache == null) {
        success = await provider.ajouterTache(tache);
      } else {
        success = await provider.modifierTache(tache);
      }

      if (success) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.tache == null
                  ? 'Tâche ajoutée avec succès'
                  : 'Tâche modifiée avec succès',
            ),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(provider.error ?? 'Erreur lors de l\'enregistrement'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getTypeLabel(TypeTache type) {
    switch (type) {
      case TypeTache.visite:
        return 'Visite';
      case TypeTache.action:
        return 'Action';
      case TypeTache.formation:
        return 'Formation';
      case TypeTache.inventaire:
        return 'Inventaire';
      case TypeTache.promotion:
        return 'Promotion';
      case TypeTache.maintenance:
        return 'Maintenance';
      case TypeTache.autre:
        return 'Autre';
    }
  }

  String _getPrioriteLabel(PrioriteTache priorite) {
    switch (priorite) {
      case PrioriteTache.basse:
        return 'Basse';
      case PrioriteTache.normale:
        return 'Normale';
      case PrioriteTache.haute:
        return 'Haute';
      case PrioriteTache.urgente:
        return 'Urgente';
    }
  }

  @override
  void dispose() {
    _titreController.dispose();
    _descriptionController.dispose();
    _commentairesController.dispose();
    super.dispose();
  }
}

class _DureeDialog extends StatefulWidget {
  final Duration? dureeInitiale;

  const _DureeDialog({this.dureeInitiale});

  @override
  State<_DureeDialog> createState() => _DureeDialogState();
}

class _DureeDialogState extends State<_DureeDialog> {
  int _heures = 0;
  int _minutes = 0;

  @override
  void initState() {
    super.initState();
    if (widget.dureeInitiale != null) {
      _heures = widget.dureeInitiale!.inHours;
      _minutes = widget.dureeInitiale!.inMinutes % 60;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Durée estimée'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  initialValue: _heures.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Heures',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _heures = int.tryParse(value) ?? 0;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  initialValue: _minutes.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Minutes',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _minutes = int.tryParse(value) ?? 0;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: () {
            final duree = Duration(hours: _heures, minutes: _minutes);
            Navigator.pop(context, duree);
          },
          child: const Text('Valider'),
        ),
      ],
    );
  }
}
