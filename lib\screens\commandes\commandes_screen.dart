import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/commande_provider.dart';
import '../../models/commande.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../../widgets/professional_ui_components.dart';
import 'nouvelle_commande_screen.dart';
import 'commande_detail_screen.dart';
import 'facture_screen.dart';

class CommandesScreen extends StatefulWidget {
  const CommandesScreen({super.key});

  @override
  State<CommandesScreen> createState() => _CommandesScreenState();
}

class _CommandesScreenState extends State<CommandesScreen> {
  StatutCommande? _selectedStatut;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 28, showText: false),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Commandes',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 22,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Gestion des commandes clients',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFFF59E0B).withValues(alpha: 0.05),
                      const Color(0xFFD97706).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: isSmallScreen ? 20 : 30,
                      right: isSmallScreen ? 10 : 15,
                      child: Container(
                        width: isSmallScreen ? 60 : 80,
                        height: isSmallScreen ? 60 : 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFFF59E0B).withValues(alpha: 0.1),
                              const Color(0xFFD97706).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.shopping_cart_outlined,
                            size: isSmallScreen ? 24 : 32,
                            color: const Color(0xFFF59E0B),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              Container(
                margin: EdgeInsets.only(right: isSmallScreen ? 12 : 16, top: 8),
                child: FilledButton.icon(
                  onPressed: () => _naviguerVersNouvelleCommande(context),
                  icon: Icon(Icons.add, size: isSmallScreen ? 16 : 18),
                  label: Text(isSmallScreen ? 'Nouveau' : 'Nouveau'),
                  style: FilledButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 12 : 16,
                      vertical: isSmallScreen ? 6 : 8,
                    ),
                    backgroundColor: const Color(0xFFF59E0B),
                    textStyle: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                  ),
                ),
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.fromLTRB(padding, 16, padding, 8),
              child: Column(
                children: [
                  // Filtres par statut optimisés
                  SizedBox(
                    height: isSmallScreen ? 36 : 40,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: [
                        _buildStatutFilter('Toutes', null, isSmallScreen),
                        SizedBox(width: isSmallScreen ? 6 : 8),
                        ...StatutCommande.values.map(
                          (statut) => Padding(
                            padding: EdgeInsets.only(
                              right: isSmallScreen ? 6 : 8,
                            ),
                            child: _buildStatutFilter(
                              _getStatutLabel(statut),
                              statut,
                              isSmallScreen,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Liste des commandes
          SliverFillRemaining(
            child: Consumer<CommandeProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (provider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          provider.error!,
                          style: Theme.of(context).textTheme.bodyLarge,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            provider.effacerErreur();
                            provider.chargerCommandes();
                          },
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  );
                }

                final commandes =
                    _selectedStatut == null
                        ? provider.commandes
                        : provider.commandes
                            .where((c) => c.statut == _selectedStatut)
                            .toList();
                if (commandes.isEmpty) {
                  return ModernEmptyState(
                    icon: Icons.shopping_cart_outlined,
                    title: 'Aucune commande trouvée',
                    subtitle: 'Prenez votre première commande client',
                    actionText: 'Nouvelle commande',
                    onAction: () => _naviguerVersNouvelleCommande(context),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => provider.chargerCommandes(),
                  child: ListView.builder(
                    padding: EdgeInsets.symmetric(horizontal: padding),
                    itemCount: commandes.length,
                    itemBuilder: (context, index) {
                      final commande = commandes[index];
                      return _buildCommandeCard(
                        context,
                        commande,
                        isSmallScreen,
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatutFilter(
    String label,
    StatutCommande? statut,
    bool isSmallScreen,
  ) {
    final isSelected = _selectedStatut == statut;
    return FilterChip(
      label: Text(label, style: TextStyle(fontSize: isSmallScreen ? 12 : 14)),
      selected: isSelected,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 8 : 12,
        vertical: isSmallScreen ? 4 : 6,
      ),
      onSelected: (selected) {
        setState(() {
          _selectedStatut = selected ? statut : null;
        });
        if (statut == null) {
          context.read<CommandeProvider>().chargerCommandes();
        } else {
          context.read<CommandeProvider>().filtrerParStatut(statut);
        }
      },
    );
  }

  String _getStatutLabel(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'En attente';
      case StatutCommande.confirmee:
        return 'Confirmée';
      case StatutCommande.enPreparation:
        return 'En préparation';
      case StatutCommande.expediee:
        return 'Expédiée';
      case StatutCommande.livree:
        return 'Livrée';
      case StatutCommande.annulee:
        return 'Annulée';
    }
  }

  Color _getStatutColor(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return Colors.orange;
      case StatutCommande.confirmee:
        return Colors.blue;
      case StatutCommande.enPreparation:
        return Colors.purple;
      case StatutCommande.expediee:
        return Colors.indigo;
      case StatutCommande.livree:
        return Colors.green;
      case StatutCommande.annulee:
        return Colors.red;
    }
  }

  void _gererActionCommande(
    BuildContext context,
    String action,
    Commande commande,
  ) {
    switch (action) {
      case 'voir':
        _naviguerVersDetail(context, commande);
        break;
      case 'facture':
        _genererFacture(context, commande);
        break;
    }
  }

  void _naviguerVersNouvelleCommande(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NouvelleCommandeScreen()),
    );
  }

  void _naviguerVersDetail(BuildContext context, Commande commande) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommandeDetailScreen(commande: commande),
      ),
    );
  }

  void _genererFacture(BuildContext context, Commande commande) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FactureScreen(commande: commande),
      ),
    );
  }

  Widget _buildStatutBadge(StatutCommande statut, bool isSmallScreen) {
    switch (statut) {
      case StatutCommande.enAttente:
        return StatusBadge.warning(
          text: _getStatutLabel(statut),
          fontSize: isSmallScreen ? 11 : 12,
        );
      case StatutCommande.confirmee:
        return StatusBadge.info(
          text: _getStatutLabel(statut),
          fontSize: isSmallScreen ? 11 : 12,
        );
      case StatutCommande.enPreparation:
        return StatusBadge.info(
          text: _getStatutLabel(statut),
          fontSize: isSmallScreen ? 11 : 12,
        );
      case StatutCommande.expediee:
        return StatusBadge.info(
          text: _getStatutLabel(statut),
          fontSize: isSmallScreen ? 11 : 12,
        );
      case StatutCommande.livree:
        return StatusBadge.success(
          text: _getStatutLabel(statut),
          fontSize: isSmallScreen ? 11 : 12,
        );
      case StatutCommande.annulee:
        return StatusBadge.error(
          text: _getStatutLabel(statut),
          fontSize: isSmallScreen ? 11 : 12,
        );
    }
  }

  Widget _buildCommandeCard(
    BuildContext context,
    Commande commande,
    bool isSmallScreen,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
      child: ProfessionalCard(
        onTap: () => _naviguerVersDetail(context, commande),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header avec numéro et statut
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 8 : 10,
                        vertical: isSmallScreen ? 4 : 6,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatutColor(commande.statut),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        '#${commande.id}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: isSmallScreen ? 12 : 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: isSmallScreen ? 8 : 12),
                    _buildStatutBadge(commande.statut, isSmallScreen),
                  ],
                ),
                PopupMenuButton<String>(
                  onSelected:
                      (value) => _gererActionCommande(context, value, commande),
                  icon: Icon(Icons.more_vert, size: isSmallScreen ? 18 : 20),
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'voir',
                          child: Row(
                            children: [
                              Icon(Icons.visibility, size: 18),
                              SizedBox(width: 8),
                              Text('Voir détails'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'facture',
                          child: Row(
                            children: [
                              Icon(Icons.receipt, size: 18),
                              SizedBox(width: 8),
                              Text('Générer facture'),
                            ],
                          ),
                        ),
                      ],
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 8 : 12),
            // Infos client et date
            Row(
              children: [
                Icon(
                  Icons.person_outline,
                  size: isSmallScreen ? 16 : 18,
                  color: Colors.grey.shade600,
                ),
                SizedBox(width: isSmallScreen ? 4 : 6),
                Expanded(
                  child: Text(
                    'Client ID: ${commande.clientId}',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 14 : 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF1F2937),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  DateFormat('dd/MM/yyyy').format(commande.dateCommande),
                  style: TextStyle(
                    fontSize: isSmallScreen ? 12 : 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
            SizedBox(height: isSmallScreen ? 6 : 8),

            // Total et nombre d'articles
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total: ${commande.montantTotal.toStringAsFixed(2)} €',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 16 : 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF10B981),
                  ),
                ),
                Text(
                  '${commande.items.length} article${commande.items.length > 1 ? 's' : ''}',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 12 : 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
