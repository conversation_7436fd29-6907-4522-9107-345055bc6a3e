import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/firebase_service.dart';

class FirebaseAuthProvider extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  User? _user;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  FirebaseAuthProvider() {
    // Écouter les changements d'état d'authentification
    _auth.authStateChanges().listen((User? user) {
      _user = user;
      notifyListeners();
    });
  }

  /// Connexion avec email et mot de passe
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = result.user;

      // Mettre à jour la dernière connexion
      if (_user != null) {
        await _updateLastLogin(_user!.uid);
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite';
      notifyListeners();
      return false;
    }
  }

  /// Inscription avec email et mot de passe
  Future<bool> signUpWithEmailAndPassword(
    String email,
    String password,
    String nom,
    String role,
  ) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = result.user;

      // Créer le profil utilisateur dans Firestore
      if (_user != null) {
        await _createUserProfile(_user!.uid, email, nom, role);
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite';
      notifyListeners();
      return false;
    }
  }

  /// Inscription avec données complètes pour commercial ou merchandiser
  Future<bool> signUpWithUserData({
    required String email,
    required String password,
    required String nomComplet,
    required String telephone,
    required String userType, // 'commercial' ou 'merchandiser'
    String? mobile,
    String? territoire,
    String status = 'Inactif',
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      print('🔥 Début de l\'inscription pour: $email, type: $userType');

      // Créer l'utilisateur avec Firebase Auth
      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      _user = result.user;
      print('🔥 Utilisateur Firebase Auth créé avec UID: ${_user?.uid}');

      // Créer le profil utilisateur dans Firestore
      if (_user != null) {
        print('🔥 Création du profil utilisateur dans Firestore...');
        await _createCompleteUserProfile(
          uid: _user!.uid,
          email: email,
          nomComplet: nomComplet,
          telephone: telephone,
          userType: userType,
          mobile: mobile,
          territoire: territoire,
          status: status,
        );
        print('🔥 Profil utilisateur créé avec succès!');
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      print('🔥 Erreur Firebase Auth: ${e.code} - ${e.message}');
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite: $e';
      print('🔥 Erreur inattendue: $e');
      notifyListeners();
      return false;
    }
  }

  /// Déconnexion
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      _user = null;
      _errorMessage = null;
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Erreur lors de la déconnexion';
      notifyListeners();
    }
  }

  /// Réinitialiser le mot de passe
  Future<bool> resetPassword(String email) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await _auth.sendPasswordResetEmail(email: email);

      _isLoading = false;
      notifyListeners();
      return true;
    } on FirebaseAuthException catch (e) {
      _isLoading = false;
      _errorMessage = _getErrorMessage(e.code);
      notifyListeners();
      return false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Une erreur inattendue s\'est produite';
      notifyListeners();
      return false;
    }
  }

  /// Obtenir le profil utilisateur depuis Firestore
  Future<Map<String, dynamic>?> getUserProfile() async {
    if (_user == null) return null;

    try {
      final doc = await FirebaseService.users.doc(_user!.uid).get();
      return doc.data() as Map<String, dynamic>?;
    } catch (e) {
      print('Erreur lors de la récupération du profil: $e');
      return null;
    }
  }

  /// Mettre à jour le profil utilisateur
  Future<bool> updateUserProfile(Map<String, dynamic> data) async {
    if (_user == null) return false;

    try {
      await FirebaseService.users.doc(_user!.uid).update(data);
      return true;
    } catch (e) {
      _errorMessage = 'Erreur lors de la mise à jour du profil';
      notifyListeners();
      return false;
    }
  }

  /// Créer le profil utilisateur dans Firestore
  Future<void> _createUserProfile(
    String uid,
    String email,
    String nom,
    String role,
  ) async {
    await FirebaseService.users.doc(uid).set({
      'email': email,
      'nom': nom,
      'role': role,
      'createdAt': FieldValue.serverTimestamp(),
      'lastLogin': FieldValue.serverTimestamp(),
    });
  }

  /// Créer le profil utilisateur complet avec données spécifiques au type
  Future<void> _createCompleteUserProfile({
    required String uid,
    required String email,
    required String nomComplet,
    required String telephone,
    required String userType,
    String? mobile,
    String? territoire,
    String status = 'Inactif',
  }) async {
    try {
      print(
        '🔥 _createCompleteUserProfile appelée avec UID: $uid, type: $userType',
      );

      // Données communes pour tous les utilisateurs
      final commonData = {
        'email': email,
        'nom': nomComplet,
        'role': userType,
        'telephone': telephone,
        'mobile': mobile,
        'territoire': territoire,
        'status': status,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLogin': FieldValue.serverTimestamp(),
      };

      print('🔥 Sauvegarde dans la collection users...');
      // Sauvegarder dans la collection users
      await FirebaseService.users.doc(uid).set(commonData);
      print('🔥 Sauvegarde dans users réussie!');

      // Sauvegarder dans la collection spécifique selon le type d'utilisateur
      if (userType == 'commercial') {
        print('🔥 Sauvegarde dans la collection commercials...');
        await FirebaseService.commercials.doc(uid).set({
          'userId': uid,
          'email': email,
          'nom': nomComplet,
          'telephone': telephone,
          'mobile': mobile,
          'territoire': territoire,
          'status': status,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        print('🔥 Sauvegarde dans commercials réussie!');
      } else if (userType == 'merchandiser') {
        print('🔥 Sauvegarde dans la collection merchandisers...');
        await FirebaseService.merchandisers.doc(uid).set({
          'userId': uid,
          'email': email,
          'nom': nomComplet,
          'telephone': telephone,
          'mobile': mobile,
          'zone': territoire, // Pour les merchandisers, territoire devient zone
          'status': status,
          'actif': status == 'Actif' ? true : false,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        print('🔥 Sauvegarde dans merchandisers réussie!');
      }
    } catch (e) {
      print('🔥 Erreur lors de la création du profil: $e');
      rethrow;
    }
  }

  /// Mettre à jour la dernière connexion
  Future<void> _updateLastLogin(String uid) async {
    await FirebaseService.users.doc(uid).update({
      'lastLogin': FieldValue.serverTimestamp(),
    });
  }

  /// Obtenir le message d'erreur en français
  String _getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'Aucun utilisateur trouvé avec cet email';
      case 'wrong-password':
        return 'Mot de passe incorrect';
      case 'email-already-in-use':
        return 'Cet email est déjà utilisé';
      case 'weak-password':
        return 'Le mot de passe est trop faible';
      case 'invalid-email':
        return 'Email invalide';
      case 'user-disabled':
        return 'Ce compte utilisateur a été désactivé';
      case 'too-many-requests':
        return 'Trop de tentatives, réessayez plus tard';
      default:
        return 'Erreur d\'authentification';
    }
  }

  /// Effacer le message d'erreur
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
