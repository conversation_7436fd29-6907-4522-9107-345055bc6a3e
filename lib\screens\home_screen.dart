import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/firebase_client_provider.dart';
import '../providers/produit_provider.dart';
import '../providers/commande_provider.dart';
import '../widgets/vitabrosse_logo.dart';
import '../widgets/professional_ui_components.dart';
import 'clients/clients_screen.dart';
import 'produits/produits_screen.dart';
import 'commandes/commandes_screen.dart';
import 'devis/devis_screen.dart';
import 'catalogue/catalogue_screen.dart';
import 'merchandising/merchandising_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const DashboardTab(),
    const ClientsScreen(),
    const ProduitsScreen(),
    const CommandesScreen(),
    const DevisScreen(),
    const CatalogueScreen(),
    const MerchandisingScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Utiliser addPostFrameCallback pour éviter setState pendant build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerDonnees();
    });
  }

  Future<void> _chargerDonnees() async {
    final clientProvider = Provider.of<FirebaseClientProvider>(
      context,
      listen: false,
    );
    final produitProvider = Provider.of<ProduitProvider>(
      context,
      listen: false,
    );
    final commandeProvider = Provider.of<CommandeProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      clientProvider.loadClients(),
      produitProvider.chargerProduits(),
      produitProvider.chargerCategories(),
      commandeProvider.chargerCommandes(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _selectedIndex,
          onTap: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.dashboard_outlined),
              activeIcon: Icon(Icons.dashboard),
              label: 'Tableau de bord',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people_outline),
              activeIcon: Icon(Icons.people),
              label: 'Clients',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_2_outlined),
              activeIcon: Icon(Icons.inventory_2),
              label: 'Produits',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.shopping_cart_outlined),
              activeIcon: Icon(Icons.shopping_cart),
              label: 'Commandes',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.description_outlined),
              activeIcon: Icon(Icons.description),
              label: 'Devis',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.library_books_outlined),
              activeIcon: Icon(Icons.library_books),
              label: 'Catalogues',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.store_outlined),
              activeIcon: Icon(Icons.store),
              label: 'Merchandising',
            ),
          ],
        ),
      ),
    );
  }
}

class DashboardTab extends StatelessWidget {
  const DashboardTab({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 160,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 32, showText: false),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'VitaBrosse®',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: Color(0xFF1F2937),
                            fontSize: 20,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Gestion Commerciale Professionnelle',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF6B7280),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF6366F1).withValues(alpha: 0.05),
                      const Color(0xFF8B5CF6).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: 40,
                      right: 20,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF6366F1).withValues(alpha: 0.1),
                              const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 60,
                      right: 60,
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF8B5CF6).withValues(alpha: 0.15),
                              const Color(0xFF6366F1).withValues(alpha: 0.15),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(
                MediaQuery.of(context).size.width < 480 ? 16.0 : 20.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bienvenue !',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF1F2937),
                      fontSize:
                          MediaQuery.of(context).size.width < 360
                              ? 20
                              : MediaQuery.of(context).size.width < 480
                              ? 22
                              : 24,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Voici un aperçu de votre activité commerciale',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey.shade600,
                      fontSize:
                          MediaQuery.of(context).size.width < 360
                              ? 13
                              : MediaQuery.of(context).size.width < 480
                              ? 14
                              : 16,
                    ),
                  ),
                  SizedBox(
                    height: MediaQuery.of(context).size.width < 480 ? 24 : 32,
                  ), // Statistics Cards - Responsive Layout
                  _buildResponsiveStatCards(context),

                  SizedBox(
                    height: MediaQuery.of(context).size.width < 480 ? 24 : 32,
                  ),

                  // Quick Actions
                  SectionHeader(
                    title: 'Actions rapides',
                    subtitle: 'Accès rapide aux fonctionnalités principales',
                  ),

                  // Quick Actions - Responsive Layout
                  _buildResponsiveQuickActions(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    Widget valueWidget,
  ) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, size: 18, color: color),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(child: valueWidget),
        ],
      ),
    );
  }

  Widget _buildCardContent(String value, String subtitle) {
    return Builder(
      builder: (context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isVerySmallScreen = screenWidth < 360;
        final isSmallScreen = screenWidth < 480;

        // Ajuster les tailles de police selon l'écran
        final valueFontSize =
            isVerySmallScreen ? 14.0 : (isSmallScreen ? 16.0 : 20.0);
        final subtitleFontSize =
            isVerySmallScreen ? 9.0 : (isSmallScreen ? 10.0 : 11.0);

        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment:
              MainAxisAlignment.center, // Centre le contenu verticalement
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: valueFontSize,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1F2937),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(height: 1),
            Flexible(
              child: Text(
                subtitle,
                style: TextStyle(
                  fontSize: subtitleFontSize,
                  color: Colors.grey.shade500,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ProfessionalCard(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(icon, size: 18, color: color),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponsiveStatCards(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isVerySmallScreen = screenWidth < 360; // iPhone SE et similaires
    final isSmallScreen = screenWidth < 480; // Petits téléphones
    final isMediumScreen = screenWidth < 600; // Grands téléphones

    final cards = [
      _buildStatCard(
        context,
        'Clients',
        Icons.people_outline,
        const Color(0xFF3B82F6),
        Consumer<FirebaseClientProvider>(
          builder: (context, provider, child) {
            return FutureBuilder<Map<String, dynamic>>(
              future: provider.obtenirStatistiques(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return _buildCardContent(
                    '${snapshot.data!['nombreTotal']}',
                    'Total',
                  );
                }
                return const ModernLoadingIndicator(size: 16);
              },
            );
          },
        ),
      ),
      _buildStatCard(
        context,
        'Produits',
        Icons.inventory_2_outlined,
        const Color(0xFF10B981),
        Consumer<ProduitProvider>(
          builder: (context, provider, child) {
            return FutureBuilder<Map<String, dynamic>>(
              future: provider.obtenirStatistiques(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return _buildCardContent(
                    '${snapshot.data!['nombreTotal']}',
                    'En stock',
                  );
                }
                return const ModernLoadingIndicator(size: 16);
              },
            );
          },
        ),
      ),
      _buildStatCard(
        context,
        'Commandes',
        Icons.shopping_cart_outlined,
        const Color(0xFFF59E0B),
        Consumer<CommandeProvider>(
          builder: (context, provider, child) {
            return FutureBuilder<Map<String, dynamic>>(
              future: provider.obtenirStatistiques(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return _buildCardContent(
                    '${snapshot.data!['nombreTotal']}',
                    'Ce mois',
                  );
                }
                return const ModernLoadingIndicator(size: 16);
              },
            );
          },
        ),
      ),
      _buildStatCard(
        context,
        'Revenus',
        Icons.euro_outlined,
        const Color(0xFF8B5CF6),
        Consumer<CommandeProvider>(
          builder: (context, provider, child) {
            return FutureBuilder<Map<String, dynamic>>(
              future: provider.obtenirStatistiques(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final ca = snapshot.data!['chiffreAffaires'] as double;
                  return _buildCardContent(
                    '${(ca / 1000).toStringAsFixed(1)}k€',
                    'Total',
                  );
                }
                return Center(
                  child: SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: const Color(0xFF8B5CF6),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    ];

    if (isMediumScreen) {
      // Sur écrans de téléphone : défilement horizontal optimisé
      final cardWidth =
          isVerySmallScreen ? 120.0 : (isSmallScreen ? 140.0 : 160.0);
      final cardHeight =
          isVerySmallScreen
              ? 110.0
              : (isSmallScreen
                  ? 120.0
                  : 130.0); // Augmenté pour éviter l'overflow

      return SizedBox(
        height: cardHeight,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 4),
          itemCount: cards.length,
          separatorBuilder:
              (context, index) => SizedBox(width: isSmallScreen ? 8 : 12),
          itemBuilder: (context, index) {
            return SizedBox(width: cardWidth, child: cards[index]);
          },
        ),
      );
    } else {
      // Sur tablettes et écrans plus grands : grille 2x2
      return GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 2.5, // Augmenté pour plus d'espace
        children: cards,
      );
    }
  }

  Widget _buildResponsiveQuickActions(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600; // Breakpoint pour téléphone

    final actions = [
      {
        'title': 'Nouveau client',
        'icon': Icons.person_add_outlined,
        'color': const Color(0xFF3B82F6),
        'onTap': () => Navigator.pushNamed(context, '/clients/form'),
      },
      {
        'title': 'Nouvelle commande',
        'icon': Icons.add_shopping_cart_outlined,
        'color': const Color(0xFF10B981),
        'onTap': () => Navigator.pushNamed(context, '/commandes/form'),
      },
      {
        'title': 'Nouveau produit',
        'icon': Icons.add_box_outlined,
        'color': const Color(0xFFF59E0B),
        'onTap': () => Navigator.pushNamed(context, '/produits/form'),
      },
      {
        'title': 'Nouveau devis',
        'icon': Icons.note_add_outlined,
        'color': const Color(0xFF8B5CF6),
        'onTap': () => Navigator.pushNamed(context, '/devis/form'),
      },
      {
        'title': 'Nouveau catalogue',
        'icon': Icons.library_books_outlined,
        'color': const Color(0xFFEF4444),
        'onTap': () => Navigator.pushNamed(context, '/catalogues/form'),
      },
    ];

    if (isSmallScreen) {
      // Sur téléphones : optimisation horizontale avec 2 colonnes pour les actions courtes
      final isVerySmallScreen = screenWidth < 360;

      if (isVerySmallScreen) {
        // Très petits écrans : une seule colonne
        return Column(
          children:
              actions.asMap().entries.map((entry) {
                final index = entry.key;
                final action = entry.value;
                return Column(
                  children: [
                    _buildQuickActionCard(
                      context,
                      action['title'] as String,
                      action['icon'] as IconData,
                      action['color'] as Color,
                      action['onTap'] as VoidCallback,
                    ),
                    if (index < actions.length - 1) const SizedBox(height: 8),
                  ],
                );
              }).toList(),
        );
      } else {
        // Petits écrans : 2 colonnes pour optimiser l'espace horizontal
        return Column(
          children: [
            // Première ligne : 2 actions
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    actions[0]['title'] as String,
                    actions[0]['icon'] as IconData,
                    actions[0]['color'] as Color,
                    actions[0]['onTap'] as VoidCallback,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    actions[1]['title'] as String,
                    actions[1]['icon'] as IconData,
                    actions[1]['color'] as Color,
                    actions[1]['onTap'] as VoidCallback,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Deuxième ligne : 2 actions
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    actions[2]['title'] as String,
                    actions[2]['icon'] as IconData,
                    actions[2]['color'] as Color,
                    actions[2]['onTap'] as VoidCallback,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    actions[3]['title'] as String,
                    actions[3]['icon'] as IconData,
                    actions[3]['color'] as Color,
                    actions[3]['onTap'] as VoidCallback,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Troisième ligne : 1 action centrée
            _buildQuickActionCard(
              context,
              actions[4]['title'] as String,
              actions[4]['icon'] as IconData,
              actions[4]['color'] as Color,
              actions[4]['onTap'] as VoidCallback,
            ),
          ],
        );
      }
    } else {
      // Sur tablettes et écrans plus grands : optimisation horizontale maximale
      final isLargeScreen = screenWidth > 900;

      if (isLargeScreen) {
        // Très grands écrans : toutes les actions sur une seule ligne
        return Row(
          children:
              actions.map((action) {
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: _buildQuickActionCard(
                      context,
                      action['title'] as String,
                      action['icon'] as IconData,
                      action['color'] as Color,
                      action['onTap'] as VoidCallback,
                    ),
                  ),
                );
              }).toList(),
        );
      } else {
        // Écrans moyens : grille 3x2 pour optimiser l'espace
        return Column(
          children: [
            // Première ligne : 3 actions
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    actions[0]['title'] as String,
                    actions[0]['icon'] as IconData,
                    actions[0]['color'] as Color,
                    actions[0]['onTap'] as VoidCallback,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    actions[1]['title'] as String,
                    actions[1]['icon'] as IconData,
                    actions[1]['color'] as Color,
                    actions[1]['onTap'] as VoidCallback,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    actions[2]['title'] as String,
                    actions[2]['icon'] as IconData,
                    actions[2]['color'] as Color,
                    actions[2]['onTap'] as VoidCallback,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Deuxième ligne : 2 actions centrées
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    actions[3]['title'] as String,
                    actions[3]['icon'] as IconData,
                    actions[3]['color'] as Color,
                    actions[3]['onTap'] as VoidCallback,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionCard(
                    context,
                    actions[4]['title'] as String,
                    actions[4]['icon'] as IconData,
                    actions[4]['color'] as Color,
                    actions[4]['onTap'] as VoidCallback,
                  ),
                ),
                // Espace vide pour centrer les 2 actions
                const Expanded(child: SizedBox()),
              ],
            ),
          ],
        );
      }
    }
  }
}
