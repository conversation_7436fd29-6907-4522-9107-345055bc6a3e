import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/tache_merchandising_provider.dart';
import '../../providers/merchandiser_provider.dart';
import '../../models/tache_merchandising.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'ajouter_tache_screen.dart';
import 'detail_tache_screen.dart';

class CalendrierMerchandisingScreen extends StatefulWidget {
  const CalendrierMerchandisingScreen({super.key});

  @override
  State<CalendrierMerchandisingScreen> createState() =>
      _CalendrierMerchandisingScreenState();
}

class _CalendrierMerchandisingScreenState
    extends State<CalendrierMerchandisingScreen> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerDonnees();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _chargerDonnees() async {
    final tacheProvider = Provider.of<TacheMerchandisingProvider>(
      context,
      listen: false,
    );
    final merchandiserProvider = Provider.of<MerchandiserProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      tacheProvider.chargerTaches(),
      merchandiserProvider.chargerMerchandisers(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const VitaBrosseLogo(height: 28, showText: false),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Calendrier Merchandising',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 18 : 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Planification des tâches',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          Consumer<TacheMerchandisingProvider>(
            builder: (context, provider, child) {
              return Badge(
                label: Text('${provider.tachesEnRetard.length}'),
                isLabelVisible: provider.tachesEnRetard.isNotEmpty,
                child: IconButton(
                  icon: const Icon(Icons.warning_amber),
                  onPressed: () => _afficherTachesEnRetard(context, provider),
                ),
              );
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'filtres':
                  _afficherFiltres(context);
                  break;
                case 'statistiques':
                  _afficherStatistiques(context);
                  break;
                case 'actualiser':
                  _chargerDonnees();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'filtres',
                    child: Row(
                      children: [
                        Icon(Icons.filter_alt),
                        SizedBox(width: 8),
                        Text('Filtres'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'statistiques',
                    child: Row(
                      children: [
                        Icon(Icons.analytics),
                        SizedBox(width: 8),
                        Text('Statistiques'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'actualiser',
                    child: Row(
                      children: [
                        Icon(Icons.refresh),
                        SizedBox(width: 8),
                        Text('Actualiser'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Consumer<TacheMerchandisingProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    provider.error!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _chargerDonnees,
                    child: const Text('Réessayer'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // Calendrier personnalisé simple
                Card(
                  margin: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // Header du calendrier
                      Container(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.chevron_left),
                              onPressed: () {
                                setState(() {
                                  _focusedDay = DateTime(
                                    _focusedDay.year,
                                    _focusedDay.month - 1,
                                    1,
                                  );
                                });
                              },
                            ),
                            Expanded(
                              child: Text(
                                '${_getMonthName(_focusedDay.month)} ${_focusedDay.year}',
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.chevron_right),
                              onPressed: () {
                                setState(() {
                                  _focusedDay = DateTime(
                                    _focusedDay.year,
                                    _focusedDay.month + 1,
                                    1,
                                  );
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                      // Grille du calendrier
                      _buildCalendarGrid(provider),
                    ],
                  ),
                ),

                // Résumé du jour sélectionné
                if (_selectedDay != null)
                  Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: _buildResumeDuJour(provider),
                  ),

                // Liste des tâches du jour sélectionné
                Container(
                  height: MediaQuery.of(context).size.height * 0.4,
                  child: _buildListeTachesDuJour(provider),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _ajouterTache(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildResumeDuJour(TacheMerchandisingProvider provider) {
    final tachesDuJour = provider.obtenirTachesParDate(_selectedDay!);
    final tachesTerminees =
        tachesDuJour.where((t) => t.statut == StatutTache.terminee).length;
    final tachesEnCours =
        tachesDuJour.where((t) => t.statut == StatutTache.en_cours).length;
    final tachesEnRetard = tachesDuJour.where((t) => t.estEnRetard).length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Résumé du ${_selectedDay!.day}/${_selectedDay!.month}/${_selectedDay!.year}',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                'Total',
                tachesDuJour.length.toString(),
                Colors.blue,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                'Terminées',
                tachesTerminees.toString(),
                Colors.green,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                'En cours',
                tachesEnCours.toString(),
                Colors.orange,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                'En retard',
                tachesEnRetard.toString(),
                Colors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildListeTachesDuJour(TacheMerchandisingProvider provider) {
    if (_selectedDay == null) return const SizedBox();

    final tachesDuJour = provider.obtenirTachesParDate(_selectedDay!);

    if (tachesDuJour.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_available, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Aucune tâche prévue pour ce jour',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: tachesDuJour.length,
      itemBuilder: (context, index) {
        final tache = tachesDuJour[index];
        return _buildTacheCard(tache, provider);
      },
    );
  }

  Widget _buildTacheCard(
    TacheMerchandising tache,
    TacheMerchandisingProvider provider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getColorFromHex(tache.couleurPriorite),
          child: Icon(_getIconFromString(tache.iconeType), color: Colors.white),
        ),
        title: Text(
          tache.titre,
          style: TextStyle(
            decoration:
                tache.statut == StatutTache.terminee
                    ? TextDecoration.lineThrough
                    : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(tache.description),
            const SizedBox(height: 4),
            Wrap(
              spacing: 4,
              runSpacing: 4,
              children: [
                _buildChip(tache.type.name, Colors.blue),
                _buildChip(tache.statut.name, _getStatutColor(tache.statut)),
                _buildChip(
                  tache.priorite.name,
                  _getColorFromHex(tache.couleurPriorite),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleTacheAction(value, tache, provider),
          itemBuilder: (context) => _buildTacheMenuItems(tache),
        ),
        onTap: () => _voirDetailTache(tache),
      ),
    );
  }

  Widget _buildChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  List<PopupMenuEntry<String>> _buildTacheMenuItems(TacheMerchandising tache) {
    final items = <PopupMenuEntry<String>>[];

    if (tache.statut == StatutTache.planifiee) {
      items.add(
        const PopupMenuItem(
          value: 'demarrer',
          child: Row(
            children: [
              Icon(Icons.play_arrow, color: Colors.green),
              SizedBox(width: 8),
              Text('Démarrer'),
            ],
          ),
        ),
      );
    }

    if (tache.statut == StatutTache.en_cours) {
      items.add(
        const PopupMenuItem(
          value: 'terminer',
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.blue),
              SizedBox(width: 8),
              Text('Terminer'),
            ],
          ),
        ),
      );
    }

    if (tache.statut != StatutTache.terminee &&
        tache.statut != StatutTache.annulee) {
      items.add(
        const PopupMenuItem(
          value: 'reporter',
          child: Row(
            children: [
              Icon(Icons.schedule, color: Colors.orange),
              SizedBox(width: 8),
              Text('Reporter'),
            ],
          ),
        ),
      );
    }

    items.addAll([
      const PopupMenuItem(
        value: 'modifier',
        child: Row(
          children: [
            Icon(Icons.edit, color: Colors.blue),
            SizedBox(width: 8),
            Text('Modifier'),
          ],
        ),
      ),
      const PopupMenuItem(
        value: 'supprimer',
        child: Row(
          children: [
            Icon(Icons.delete, color: Colors.red),
            SizedBox(width: 8),
            Text('Supprimer'),
          ],
        ),
      ),
    ]);

    return items;
  }

  void _handleTacheAction(
    String action,
    TacheMerchandising tache,
    TacheMerchandisingProvider provider,
  ) {
    switch (action) {
      case 'demarrer':
        provider.demarrerTache(tache.id!);
        break;
      case 'terminer':
        _terminerTache(tache, provider);
        break;
      case 'reporter':
        _reporterTache(tache, provider);
        break;
      case 'modifier':
        _modifierTache(tache);
        break;
      case 'supprimer':
        _supprimerTache(tache, provider);
        break;
    }
  }

  void _ajouterTache(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => AjouterTacheScreen(
              dateInitiale: _selectedDay ?? DateTime.now(),
            ),
      ),
    );
  }

  void _voirDetailTache(TacheMerchandising tache) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => DetailTacheScreen(tache: tache)),
    );
  }

  void _modifierTache(TacheMerchandising tache) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => AjouterTacheScreen(tache: tache)),
    );
  }

  void _terminerTache(
    TacheMerchandising tache,
    TacheMerchandisingProvider provider,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Terminer la tâche'),
            content: const Text(
              'Êtes-vous sûr de vouloir marquer cette tâche comme terminée ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  provider.terminerTache(tache.id!);
                  Navigator.pop(context);
                },
                child: const Text('Terminer'),
              ),
            ],
          ),
    );
  }

  void _reporterTache(
    TacheMerchandising tache,
    TacheMerchandisingProvider provider,
  ) {
    showDatePicker(
      context: context,
      initialDate: tache.dateEcheance.add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    ).then((nouvelleDate) {
      if (nouvelleDate != null) {
        provider.reporterTache(tache.id!, nouvelleDate);
      }
    });
  }

  void _supprimerTache(
    TacheMerchandising tache,
    TacheMerchandisingProvider provider,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Supprimer la tâche'),
            content: const Text(
              'Êtes-vous sûr de vouloir supprimer cette tâche ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  provider.supprimerTache(tache.id!);
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }

  void _afficherTachesEnRetard(
    BuildContext context,
    TacheMerchandisingProvider provider,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Tâches en retard'),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: provider.tachesEnRetard.length,
                itemBuilder: (context, index) {
                  final tache = provider.tachesEnRetard[index];
                  return ListTile(
                    title: Text(tache.titre),
                    subtitle: Text(
                      'Échéance: ${tache.dateEcheance.day}/${tache.dateEcheance.month}',
                    ),
                    trailing: Icon(Icons.warning, color: Colors.red),
                    onTap: () {
                      Navigator.pop(context);
                      _voirDetailTache(tache);
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  void _afficherFiltres(BuildContext context) {
    // Implémenter l'affichage des filtres
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Filtres'),
            content: const Text('Filtres à implémenter'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  void _afficherStatistiques(BuildContext context) {
    final provider = Provider.of<TacheMerchandisingProvider>(
      context,
      listen: false,
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Statistiques'),
            content: FutureBuilder<Map<String, dynamic>>(
              future: provider.obtenirStatistiques(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final stats = snapshot.data!;
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildStatRow(
                        'Total des tâches',
                        stats['nombreTotal'].toString(),
                      ),
                      _buildStatRow(
                        'Planifiées',
                        stats['planifiees'].toString(),
                      ),
                      _buildStatRow('En cours', stats['enCours'].toString()),
                      _buildStatRow('Terminées', stats['terminees'].toString()),
                      _buildStatRow('En retard', stats['enRetard'].toString()),
                    ],
                  );
                }
                return const CircularProgressIndicator();
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Color _getColorFromHex(String hex) {
    return Color(int.parse(hex.replaceFirst('#', '0xFF')));
  }

  IconData _getIconFromString(String iconName) {
    switch (iconName) {
      case 'location_on':
        return Icons.location_on;
      case 'task_alt':
        return Icons.task_alt;
      case 'school':
        return Icons.school;
      case 'inventory':
        return Icons.inventory;
      case 'campaign':
        return Icons.campaign;
      case 'build':
        return Icons.build;
      default:
        return Icons.more_horiz;
    }
  }

  Color _getStatutColor(StatutTache statut) {
    switch (statut) {
      case StatutTache.planifiee:
        return Colors.blue;
      case StatutTache.en_cours:
        return Colors.orange;
      case StatutTache.terminee:
        return Colors.green;
      case StatutTache.reportee:
        return Colors.amber;
      case StatutTache.annulee:
        return Colors.red;
    }
  }

  Widget _buildCalendarGrid(TacheMerchandisingProvider provider) {
    final firstDayOfMonth = DateTime(_focusedDay.year, _focusedDay.month, 1);
    final lastDayOfMonth = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);
    final daysInMonth = lastDayOfMonth.day;
    final firstWeekday = firstDayOfMonth.weekday;

    // Noms des jours de la semaine
    final weekdays = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // En-tête des jours de la semaine
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children:
                weekdays
                    .map(
                      (day) => Expanded(
                        child: Container(
                          height: 40,
                          alignment: Alignment.center,
                          child: Text(
                            day,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ),
                    )
                    .toList(),
          ),
          const SizedBox(height: 8),
          // Grille des jours
          ...List.generate(6, (weekIndex) {
            // Vérifier si cette semaine contient des jours valides
            bool hasValidDays = false;
            for (int dayIndex = 0; dayIndex < 7; dayIndex++) {
              final dayNumber =
                  weekIndex * 7 + dayIndex + 1 - (firstWeekday - 1);
              if (dayNumber >= 1 && dayNumber <= daysInMonth) {
                hasValidDays = true;
                break;
              }
            }

            if (!hasValidDays) return Container();

            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: List.generate(7, (dayIndex) {
                final dayNumber =
                    weekIndex * 7 + dayIndex + 1 - (firstWeekday - 1);

                if (dayNumber < 1 || dayNumber > daysInMonth) {
                  return Expanded(child: Container(height: 40));
                }

                final currentDay = DateTime(
                  _focusedDay.year,
                  _focusedDay.month,
                  dayNumber,
                );
                final isSelected =
                    _selectedDay != null &&
                    _isSameDay(_selectedDay!, currentDay);
                final isToday = _isSameDay(DateTime.now(), currentDay);
                final tachesJour = provider.obtenirTachesParDate(currentDay);

                return Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedDay = currentDay;
                      });
                      provider.setSelectedDate(currentDay);
                    },
                    child: Container(
                      height: 40,
                      margin: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? Colors.blue
                                : isToday
                                ? Colors.orange.withOpacity(0.3)
                                : null,
                        borderRadius: BorderRadius.circular(20),
                        border:
                            isToday && !isSelected
                                ? Border.all(color: Colors.orange, width: 2)
                                : null,
                      ),
                      child: Stack(
                        children: [
                          Center(
                            child: Text(
                              dayNumber.toString(),
                              style: TextStyle(
                                color: isSelected ? Colors.white : Colors.black,
                                fontWeight:
                                    isToday
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                          ),
                          if (tachesJour.isNotEmpty)
                            Positioned(
                              right: 2,
                              top: 2,
                              child: Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Text(
                                    tachesJour.length.toString(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 8,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            );
          }),
        ],
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'Janvier',
      'Février',
      'Mars',
      'Avril',
      'Mai',
      'Juin',
      'Juillet',
      'Août',
      'Septembre',
      'Octobre',
      'Novembre',
      'Décembre',
    ];
    return months[month - 1];
  }

  bool _isSameDay(DateTime day1, DateTime day2) {
    return day1.year == day2.year &&
        day1.month == day2.month &&
        day1.day == day2.day;
  }
}
