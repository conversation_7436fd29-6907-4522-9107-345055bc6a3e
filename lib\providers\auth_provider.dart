import 'package:flutter/foundation.dart';

class AuthProvider extends ChangeNotifier {
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _currentUser;
  String? _userType;
  String? _error;

  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get currentUser => _currentUser;
  String? get userType => _userType;
  String? get error => _error;

  // Simuler une connexion
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _setError(null);

    try {
      // Simulation d'un délai de connexion
      await Future.delayed(const Duration(seconds: 2));

      // Validation simple pour la démo
      if (email.isNotEmpty && password.length >= 6) {
        // Vérification du statut de l'utilisateur (dans une vraie application, 
        // cette vérification se ferait côté serveur)
        String userStatus = await _getUserStatus(email);
        
        if (userStatus != 'Actif') {
          _setError('Votre compte n\'est pas encore activé. Veuillez contacter un administrateur.');
          return false;
        }
        
        _isAuthenticated = true;
        _currentUser = email;
        // Simuler la détection du type d'utilisateur basé sur l'email
        _userType =
            email.contains('merchandiser') ? 'merchandiser' : 'commercial';
        notifyListeners();
        return true;
      } else {
        _setError('Email ou mot de passe invalide');
        return false;
      }
    } catch (e) {
      _setError('Erreur de connexion : $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Simuler une inscription
  Future<bool> signup({
    required String nomComplet,
    required String email,
    required String password,
    required String telephone,
    String? mobile,
    String? territoire,
    required String userType,
    String status = 'Inactif', // Par défaut, le statut est inactif
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      // Simulation d'un délai d'inscription
      await Future.delayed(const Duration(seconds: 2));

      // Validation simple pour la démo
      if (email.isNotEmpty &&
          password.length >= 6 &&
          nomComplet.isNotEmpty &&
          telephone.isNotEmpty) {
          
        // Note: Dans une vraie application, nous ne ferions pas cette vérification ici
        // car l'utilisateur ne devrait pas être automatiquement authentifié après l'inscription
        // Un admin doit d'abord activer son compte (changer son statut à 'Actif')
        
        // Pour la démo, on ne s'authentifie pas automatiquement, sauf si le statut est actif
        _isAuthenticated = status == 'Actif';
        _currentUser = email;
        _userType = userType;
        notifyListeners();
        return true;
      } else {
        _setError('Veuillez remplir tous les champs correctement');
        return false;
      }
    } catch (e) {
      _setError('Erreur d\'inscription : $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Déconnexion
  void logout() {
    _isAuthenticated = false;
    _currentUser = null;
    _setError(null);
    notifyListeners();
  }

  // Continuer en tant qu'invité
  void loginAsGuest() {
    _isAuthenticated = true;
    _currentUser = 'Invité';
    _setError(null);
    notifyListeners();
  }

  // Vérifier si l'utilisateur est connecté (au démarrage de l'app)
  Future<void> checkAuthStatus() async {
    _setLoading(true);

    try {
      // Ici on pourrait vérifier le token sauvegardé, etc.
      await Future.delayed(const Duration(milliseconds: 500));

      // Pour la démo, on considère que l'utilisateur n'est pas connecté
      _isAuthenticated = false;
      _currentUser = null;
    } catch (e) {
      _setError('Erreur de vérification : $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void clearError() {
    _setError(null);
  }
  
  // Simuler la vérification du statut utilisateur
  Future<String> _getUserStatus(String email) async {
    // Dans une vraie application, cette méthode ferait une requête au backend
    // pour vérifier le statut de l'utilisateur dans la base de données
    
    // Pour la démonstration, on considère que tous les utilisateurs avec 'admin' dans leur email sont actifs
    // et les autres sont inactifs par défaut
    if (email.contains('admin')) {
      return 'Actif';
    }
    
    // Pour tester facilement, les utilisateurs avec 'actif' dans leur email sont aussi considérés comme actifs
    if (email.contains('actif')) {
      return 'Actif';
    }
    
    // Par défaut, retourner inactif pour simuler la nécessité d'activation par un admin
    return 'Inactif';
  }
}
