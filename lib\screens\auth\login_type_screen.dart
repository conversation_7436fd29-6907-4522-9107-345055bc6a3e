import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../home_screen.dart';
import '../merchandiser/merchandiser_home_screen.dart';

class LoginTypeScreen extends StatefulWidget {
  final String userType; // 'commercial' ou 'merchandiser'

  const LoginTypeScreen({super.key, required this.userType});

  @override
  State<LoginTypeScreen> createState() => _LoginTypeScreenState();
}

class _LoginTypeScreenState extends State<LoginTypeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState?.validate() ?? false) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Ici vous pourrez adapter selon votre AuthProvider
      final success = await authProvider.login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (mounted && success) {
        // Rediriger selon le type d'utilisateur
        if (widget.userType == 'commercial') {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        } else if (widget.userType == 'merchandiser') {
          // Différer la navigation pour éviter les problèmes de setState pendant build
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => MerchandiserHomeScreen()),
            );
          });
        }
      } else if (mounted && authProvider.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final isDesktop = size.width > 1200;

    final isCommercial = widget.userType == 'commercial';
    final color = isCommercial ? Colors.blue : Colors.green;
    final icon = isCommercial ? Icons.business_center : Icons.store;
    final title =
        isCommercial ? 'Connexion Commercial' : 'Connexion Merchandiser';

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.grey.shade800),
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal:
                  isDesktop
                      ? size.width * 0.3
                      : isTablet
                      ? size.width * 0.2
                      : 24.0,
              vertical: 24.0,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo et titre
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical:
                        isDesktop
                            ? 40
                            : isTablet
                            ? 32
                            : 24,
                  ),
                  child: Column(
                    children: [
                      VitaBrosseLogo(
                        height:
                            isDesktop
                                ? 80
                                : isTablet
                                ? 70
                                : 60,
                      ),
                      SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(icon, size: 28, color: color),
                          SizedBox(width: 12),
                          Text(
                            title,
                            style: TextStyle(
                              fontSize:
                                  isDesktop
                                      ? 28
                                      : isTablet
                                      ? 24
                                      : 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Connectez-vous à votre compte ${widget.userType}',
                        style: TextStyle(
                          fontSize:
                              isDesktop
                                  ? 16
                                  : isTablet
                                  ? 14
                                  : 12,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // Formulaire de connexion
                Container(
                  constraints: BoxConstraints(
                    maxWidth: isDesktop ? 400 : double.infinity,
                  ),
                  child: Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(24),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Email
                            TextFormField(
                              controller: _emailController,
                              keyboardType: TextInputType.emailAddress,
                              decoration: InputDecoration(
                                labelText: 'Email',
                                hintText: '<EMAIL>',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(Icons.email),
                              ),
                              validator: (value) {
                                if (value?.isEmpty ?? true) {
                                  return 'Email requis';
                                }
                                if (!RegExp(
                                  r'^[^@]+@[^@]+\.[^@]+',
                                ).hasMatch(value!)) {
                                  return 'Email invalide';
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),

                            // Mot de passe
                            TextFormField(
                              controller: _passwordController,
                              obscureText: !_isPasswordVisible,
                              decoration: InputDecoration(
                                labelText: 'Mot de passe',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(Icons.lock),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _isPasswordVisible
                                        ? Icons.visibility
                                        : Icons.visibility_off,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _isPasswordVisible = !_isPasswordVisible;
                                    });
                                  },
                                ),
                              ),
                              validator: (value) {
                                if (value?.isEmpty ?? true) {
                                  return 'Mot de passe requis';
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),

                            // Se souvenir de moi
                            Row(
                              children: [
                                Checkbox(
                                  value: _rememberMe,
                                  onChanged: (value) {
                                    setState(() {
                                      _rememberMe = value ?? false;
                                    });
                                  },
                                ),
                                Text('Se souvenir de moi'),
                                Spacer(),
                                TextButton(
                                  onPressed: () {
                                    // Implémenter la récupération de mot de passe
                                  },
                                  child: Text('Mot de passe oublié ?'),
                                ),
                              ],
                            ),
                            SizedBox(height: 24),

                            // Bouton de connexion
                            Consumer<AuthProvider>(
                              builder: (context, authProvider, child) {
                                return ElevatedButton(
                                  onPressed:
                                      authProvider.isLoading
                                          ? null
                                          : _handleLogin,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: color,
                                    foregroundColor: Colors.white,
                                    padding: EdgeInsets.symmetric(vertical: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  child:
                                      authProvider.isLoading
                                          ? SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                              strokeWidth: 2,
                                            ),
                                          )
                                          : Text(
                                            'Se connecter',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                SizedBox(height: 24),

                // Lien vers l'inscription
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    'Créer un compte ${widget.userType}',
                    style: TextStyle(color: color, fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
