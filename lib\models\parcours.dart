class Parcours {
  final int? id;
  final String nom;
  final String description;
  final int merchandiserId;
  final DateTime dateVisite;
  final String statut; // 'planifie', 'en_cours', 'termine', 'annule'
  final List<int> magasinIds;
  final DateTime dateCreation;
  final DateTime? dateDebut;
  final DateTime? dateFin;
  final String? commentaires;

  Parcours({
    this.id,
    required this.nom,
    required this.description,
    required this.merchandiserId,
    required this.dateVisite,
    this.statut = 'planifie',
    required this.magasinIds,
    required this.dateCreation,
    this.dateDebut,
    this.dateFin,
    this.commentaires,
  });

  // Convertir un Parcours en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nom': nom,
      'description': description,
      'merchandiserId': merchandiserId,
      'dateVisite': dateVisite.toIso8601String(),
      'statut': statut,
      'magasinIds': magasinIds.join(','),
      'dateCreation': dateCreation.toIso8601String(),
      'dateDebut': dateDebut?.toIso8601String(),
      'dateFin': dateFin?.toIso8601String(),
      'commentaires': commentaires,
    };
  }

  // Créer un Parcours à partir d'une Map de la base de données
  factory Parcours.fromMap(Map<String, dynamic> map) {
    return Parcours(
      id: map['id'],
      nom: map['nom'],
      description: map['description'],
      merchandiserId: map['merchandiserId'],
      dateVisite: DateTime.parse(map['dateVisite']),
      statut: map['statut'],
      magasinIds:
          map['magasinIds'] != null && map['magasinIds'].isNotEmpty
              ? map['magasinIds']
                  .split(',')
                  .map<int>((id) => int.parse(id))
                  .toList()
              : [],
      dateCreation: DateTime.parse(map['dateCreation']),
      dateDebut:
          map['dateDebut'] != null ? DateTime.parse(map['dateDebut']) : null,
      dateFin: map['dateFin'] != null ? DateTime.parse(map['dateFin']) : null,
      commentaires: map['commentaires'],
    );
  }

  // Créer une copie du parcours avec des modifications
  Parcours copyWith({
    int? id,
    String? nom,
    String? description,
    int? merchandiserId,
    DateTime? dateVisite,
    String? statut,
    List<int>? magasinIds,
    DateTime? dateCreation,
    DateTime? dateDebut,
    DateTime? dateFin,
    String? commentaires,
  }) {
    return Parcours(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      description: description ?? this.description,
      merchandiserId: merchandiserId ?? this.merchandiserId,
      dateVisite: dateVisite ?? this.dateVisite,
      statut: statut ?? this.statut,
      magasinIds: magasinIds ?? this.magasinIds,
      dateCreation: dateCreation ?? this.dateCreation,
      dateDebut: dateDebut ?? this.dateDebut,
      dateFin: dateFin ?? this.dateFin,
      commentaires: commentaires ?? this.commentaires,
    );
  }

  // Durée estimée du parcours
  Duration? get duree {
    if (dateDebut != null && dateFin != null) {
      return dateFin!.difference(dateDebut!);
    }
    return null;
  }

  // Nombre de magasins dans le parcours
  int get nombreMagasins => magasinIds.length;

  @override
  String toString() {
    return 'Parcours{id: $id, nom: $nom, statut: $statut, merchandiserId: $merchandiserId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Parcours && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
