import 'package:flutter/material.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'login_type_screen.dart';
import 'user_type_selection_screen.dart';

class WelcomeUserTypeScreen extends StatelessWidget {
  const WelcomeUserTypeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final isDesktop = size.width > 1200;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal:
                  isDesktop
                      ? size.width * 0.3
                      : isTablet
                      ? size.width * 0.2
                      : 24.0,
              vertical: 24.0,
            ),
            child: <PERSON>umn(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo et titre principal
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical:
                        isDesktop
                            ? 40
                            : isTablet
                            ? 32
                            : 24,
                  ),
                  child: <PERSON>um<PERSON>(
                    children: [
                      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
                        height:
                            isDesktop
                                ? 120
                                : isTablet
                                ? 100
                                : 80,
                      ),
                      SizedBox(height: 24),
                      Text(
                        'Bienvenue',
                        style: TextStyle(
                          fontSize:
                              isDesktop
                                  ? 32
                                  : isTablet
                                  ? 28
                                  : 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Choisissez votre type de compte',
                        style: TextStyle(
                          fontSize:
                              isDesktop
                                  ? 18
                                  : isTablet
                                  ? 16
                                  : 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),

                // Boutons de type d'utilisateur
                Container(
                  constraints: BoxConstraints(
                    maxWidth: isDesktop ? 600 : double.infinity,
                  ),
                  child: Column(
                    children: [
                      // Bouton Commercial
                      _buildUserTypeButton(
                        context: context,
                        title: 'Commercial',
                        subtitle: 'Gestion des clients et missions',
                        icon: Icons.business_center,
                        color: Colors.blue,
                        onLoginPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) =>
                                      LoginTypeScreen(userType: 'commercial'),
                            ),
                          );
                        },
                        onSignupPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => UserTypeSelectionScreen(),
                            ),
                          );
                        },
                      ),

                      SizedBox(height: 20),

                      // Bouton Merchandiser
                      _buildUserTypeButton(
                        context: context,
                        title: 'Merchandiser',
                        subtitle: 'Missions sur le terrain',
                        icon: Icons.store,
                        color: Colors.green,
                        onLoginPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) =>
                                      LoginTypeScreen(userType: 'merchandiser'),
                            ),
                          );
                        },
                        onSignupPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => UserTypeSelectionScreen(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 40),

                // Informations supplémentaires
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Icon(Icons.info_outline, size: 32, color: Colors.blue),
                      SizedBox(height: 12),
                      Text(
                        'Première fois sur VitaBrosse Pro ?',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Créez votre compte selon votre rôle pour accéder aux fonctionnalités adaptées à vos besoins.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserTypeButton({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onLoginPressed,
    required VoidCallback onSignupPressed,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, size: 28, color: color),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Boutons d'action
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: onLoginPressed,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: color,
                      side: BorderSide(color: color),
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('Se connecter'),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: onSignupPressed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: color,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('S\'inscrire'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
