import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/rapport_provider.dart';
import '../../models/rapport.dart';
import '../../widgets/enhanced_horizontal_layouts.dart';
import 'creer_rapport_screen.dart';

class MesRapportsScreen extends StatefulWidget {
  const MesRapportsScreen({super.key});

  @override
  State<MesRapportsScreen> createState() => _MesRapportsScreenState();
}

class _MesRapportsScreenState extends State<MesRapportsScreen> {
  String _filtreStatut = 'tous';

  @override
  void initState() {
    super.initState();
    _chargerRapports();
  }

  void _chargerRapports() {
    final rapportProvider = Provider.of<RapportProvider>(
      context,
      listen: false,
    );

    // Simuler un ID de merchandiser - vous devrez adapter selon votre AuthProvider
    final merchandiserId = 'merchandiser_123';

    rapportProvider.chargerRapportsParMerchandiser(merchandiserId);
    rapportProvider.chargerRapportsBrouillon(merchandiserId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Mes rapports'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Consumer<RapportProvider>(
        builder: (context, rapportProvider, child) {
          if (rapportProvider.isLoading) {
            return Center(child: CircularProgressIndicator());
          }

          if (rapportProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, size: 64, color: Colors.red),
                  SizedBox(height: 16),
                  Text(
                    'Erreur: ${rapportProvider.error}',
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _chargerRapports,
                    child: Text('Réessayer'),
                  ),
                ],
              ),
            );
          }

          final rapports = rapportProvider.rapports;

          // Filtrer les rapports selon le statut sélectionné
          final rapportsFiltres =
              _filtreStatut == 'tous'
                  ? rapports
                  : rapports.where((r) => r.statut == _filtreStatut).toList();

          return RefreshIndicator(
            onRefresh: () async {
              _chargerRapports();
            },
            child: Column(
              children: [
                // Statistiques rapides - Layout optimisé avec animations
                _buildEnhancedStatsSection(rapports),

                // Filtres
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildFilterChip(
                          'Tous',
                          'tous',
                          rapportsFiltres.length,
                        ),
                        SizedBox(width: 8),
                        _buildFilterChip(
                          'Brouillons',
                          'brouillon',
                          rapports.where((r) => r.statut == 'brouillon').length,
                        ),
                        SizedBox(width: 8),
                        _buildFilterChip(
                          'Envoyés',
                          'envoye',
                          rapports.where((r) => r.statut == 'envoye').length,
                        ),
                        SizedBox(width: 8),
                        _buildFilterChip(
                          'Validés',
                          'valide',
                          rapports.where((r) => r.statut == 'valide').length,
                        ),
                        SizedBox(width: 8),
                        _buildFilterChip(
                          'Rejetés',
                          'rejete',
                          rapports.where((r) => r.statut == 'rejete').length,
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 16),

                Expanded(
                  child:
                      rapportsFiltres.isEmpty
                          ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.description,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Aucun rapport trouvé',
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: Colors.grey,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Commencez par créer votre premier rapport',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          )
                          : ListView.builder(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            itemCount: rapportsFiltres.length,
                            itemBuilder: (context, index) {
                              final rapport = rapportsFiltres[index];
                              return _buildRapportCard(rapport);
                            },
                          ),
                ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => CreerRapportScreen()),
          );
        },
        backgroundColor: Colors.orange,
        child: Icon(Icons.add),
      ),
    );
  }

  Widget _buildEnhancedStatsSection(List<Rapport> rapports) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    final stats = [
      StatisticItem(
        label: 'Brouillons',
        value: rapports.where((r) => r.statut == 'brouillon').length.toString(),
        color: Colors.orange,
        icon: Icons.edit_note,
        subtitle: 'En cours',
      ),
      StatisticItem(
        label: 'Envoyés',
        value: rapports.where((r) => r.statut == 'envoye').length.toString(),
        color: Colors.blue,
        icon: Icons.send,
        subtitle: 'En attente',
      ),
      StatisticItem(
        label: 'Validés',
        value: rapports.where((r) => r.statut == 'valide').length.toString(),
        color: Colors.green,
        icon: Icons.check_circle,
        subtitle: 'Approuvés',
      ),
      StatisticItem(
        label: 'Rejetés',
        value: rapports.where((r) => r.statut == 'rejete').length.toString(),
        color: Colors.red,
        icon: Icons.cancel,
        subtitle: 'À revoir',
      ),
    ];

    return EnhancedHorizontalLayouts.buildAnimatedStatsRow(
      stats: stats,
      isSmallScreen: isSmallScreen,
      animationDuration: const Duration(milliseconds: 800),
    );
  }

  Widget _buildFilterChip(String label, String value, int count) {
    final isSelected = _filtreStatut == value;
    return FilterChip(
      label: Text('$label ($count)'),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _filtreStatut = value;
        });
      },
      selectedColor: Colors.orange.shade100,
      checkmarkColor: Colors.orange,
    );
  }

  Widget _buildRapportCard(Rapport rapport) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    // Left content (main information)
    final leftContent = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Rapport du ${rapport.dateRapport.day}/${rapport.dateRapport.month}/${rapport.dateRapport.year}',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 4),
        if (rapport.missionId.isNotEmpty)
          Text(
            'Mission: ${rapport.missionId}',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
          ),
        if (rapport.commentaires?.isNotEmpty == true) ...[
          SizedBox(height: 8),
          Text(
            rapport.commentaires!,
            style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
        SizedBox(height: 8),
        Text(
          'Créé le ${rapport.dateCreation.day}/${rapport.dateCreation.month}/${rapport.dateCreation.year}',
          style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
        ),
      ],
    );

    // Right content (status, stats, actions)
    final rightContent = Column(
      crossAxisAlignment:
          isSmallScreen ? CrossAxisAlignment.start : CrossAxisAlignment.end,
      children: [
        Chip(
          label: Text(rapport.statutAffichage, style: TextStyle(fontSize: 12)),
          backgroundColor: _getStatutColor(rapport.statut),
        ),
        SizedBox(height: 12),
        if (isSmallScreen)
          _buildStatsRow(rapport)
        else
          _buildCompactStats(rapport),
        if (rapport.feedbackCommercial?.isNotEmpty == true) ...[
          SizedBox(height: 8),
          if (isSmallScreen)
            _buildFeedbackSection(rapport)
          else
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.feedback, size: 14, color: Colors.blue),
                  SizedBox(width: 4),
                  Text(
                    'Feedback',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.blue.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
        ],
        if (rapport.estBrouillon) ...[
          SizedBox(height: 8),
          TextButton(
            onPressed: () => _modifierRapport(rapport),
            child: Text('Modifier'),
          ),
        ],
      ],
    );

    return EnhancedHorizontalLayouts.buildHorizontalCard(
      leftContent: leftContent,
      rightContent: rightContent,
      isSmallScreen: isSmallScreen,
      leftFlex: 3,
      rightFlex: 2,
      onTap: () => _afficherDetailsRapport(rapport),
    );
  }

  Widget _buildStatsRow(Rapport rapport) {
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: [
        if (rapport.dureeVisite != null)
          _buildStatItem(
            Icons.access_time,
            '${rapport.dureeVisite}h',
            Colors.grey,
          ),
        if (rapport.tachesRealisees.isNotEmpty)
          _buildStatItem(
            Icons.check_circle,
            '${rapport.tachesRealisees.length} tâches',
            Colors.green,
          ),
        if (rapport.photosUrls.isNotEmpty)
          _buildStatItem(
            Icons.photo_camera,
            '${rapport.photosUrls.length} photos',
            Colors.grey,
          ),
      ],
    );
  }

  Widget _buildCompactStats(Rapport rapport) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (rapport.dureeVisite != null)
          _buildCompactStatItem(Icons.access_time, '${rapport.dureeVisite}h'),
        if (rapport.tachesRealisees.isNotEmpty)
          _buildCompactStatItem(
            Icons.check_circle,
            '${rapport.tachesRealisees.length}',
          ),
        if (rapport.photosUrls.isNotEmpty)
          _buildCompactStatItem(
            Icons.photo_camera,
            '${rapport.photosUrls.length}',
          ),
      ],
    );
  }

  Widget _buildStatItem(IconData icon, String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        SizedBox(width: 4),
        Text(text, style: TextStyle(fontSize: 12, color: Colors.grey.shade600)),
      ],
    );
  }

  Widget _buildCompactStatItem(IconData icon, String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey.shade600),
          SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackSection(Rapport rapport) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.feedback, size: 16, color: Colors.blue),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              rapport.feedbackCommercial!,
              style: TextStyle(fontSize: 12, color: Colors.blue.shade800),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'brouillon':
        return Colors.orange.shade100;
      case 'envoye':
        return Colors.blue.shade100;
      case 'valide':
        return Colors.green.shade100;
      case 'rejete':
        return Colors.red.shade100;
      default:
        return Colors.grey.shade100;
    }
  }

  void _afficherDetailsRapport(Rapport rapport) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Détails du rapport'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Date: ${rapport.dateRapport.day}/${rapport.dateRapport.month}/${rapport.dateRapport.year}',
                  ),
                  SizedBox(height: 8),
                  Text('Statut: ${rapport.statutAffichage}'),
                  SizedBox(height: 8),
                  if (rapport.dureeVisite != null)
                    Text('Durée: ${rapport.dureeVisite}h'),
                  SizedBox(height: 8),
                  if (rapport.commentaires?.isNotEmpty == true) ...[
                    Text('Commentaires:'),
                    SizedBox(height: 4),
                    Text(rapport.commentaires!),
                    SizedBox(height: 8),
                  ],
                  if (rapport.tachesRealisees.isNotEmpty) ...[
                    Text('Tâches réalisées:'),
                    ...rapport.tachesRealisees.map((t) => Text('• $t')),
                    SizedBox(height: 8),
                  ],
                  if (rapport.feedbackCommercial?.isNotEmpty == true) ...[
                    Text('Feedback commercial:'),
                    SizedBox(height: 4),
                    Text(rapport.feedbackCommercial!),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Fermer'),
              ),
            ],
          ),
    );
  }

  void _modifierRapport(Rapport rapport) {
    // Naviguer vers l'écran de modification
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Fonctionnalité de modification à implémenter'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
