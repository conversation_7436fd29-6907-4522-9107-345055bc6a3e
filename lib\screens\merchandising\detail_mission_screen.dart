import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/mission_provider.dart';
import '../../providers/rapport_provider.dart';
import '../../models/mission.dart';
import 'creer_rapport_screen.dart';

class DetailMissionScreen extends StatefulWidget {
  final Mission mission;

  const DetailMissionScreen({Key? key, required this.mission})
    : super(key: key);

  @override
  _DetailMissionScreenState createState() => _DetailMissionScreenState();
}

class _DetailMissionScreenState extends State<DetailMissionScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Détail de la mission'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (!widget.mission.estTerminee)
            PopupMenuButton(
              onSelected: (value) {
                switch (value) {
                  case 'commencer':
                    _changerStatut('en_cours');
                    break;
                  case 'terminer':
                    _changerStatut('terminee');
                    break;
                  case 'rapport':
                    _creerRapport();
                    break;
                }
              },
              itemBuilder:
                  (context) => [
                    if (widget.mission.statut == 'en_attente')
                      PopupMenuItem(
                        value: 'commencer',
                        child: Row(
                          children: [
                            Icon(Icons.play_arrow, color: Colors.blue),
                            SizedBox(width: 8),
                            Text('Commencer'),
                          ],
                        ),
                      ),
                    if (widget.mission.statut == 'en_cours')
                      PopupMenuItem(
                        value: 'terminer',
                        child: Row(
                          children: [
                            Icon(Icons.check, color: Colors.green),
                            SizedBox(width: 8),
                            Text('Terminer'),
                          ],
                        ),
                      ),
                    PopupMenuItem(
                      value: 'rapport',
                      child: Row(
                        children: [
                          Icon(Icons.description, color: Colors.orange),
                          SizedBox(width: 8),
                          Text('Créer rapport'),
                        ],
                      ),
                    ),
                  ],
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec statut
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: _getStatutColor(widget.mission.statut),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          widget.mission.titre,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      if (widget.mission.estEnRetard)
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'EN RETARD',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    widget.mission.statutAffichage,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24),

            // Informations générales
            _buildSection('Informations générales', [
              _buildInfoRow('Magasin', widget.mission.magasinNom, Icons.store),
              _buildInfoRow(
                'Priorité',
                widget.mission.prioriteAffichage,
                _getPrioriteIcon(widget.mission.priorite),
              ),
              _buildInfoRow(
                'Date d\'échéance',
                '${widget.mission.dateEcheance.day}/${widget.mission.dateEcheance.month}/${widget.mission.dateEcheance.year}',
                Icons.schedule,
              ),
              _buildInfoRow(
                'Date de création',
                '${widget.mission.dateCreation.day}/${widget.mission.dateCreation.month}/${widget.mission.dateCreation.year}',
                Icons.calendar_today,
              ),
            ]),

            SizedBox(height: 24),

            // Description
            _buildSection('Description', [
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  widget.mission.description,
                  style: TextStyle(fontSize: 16, height: 1.5),
                ),
              ),
            ]),

            SizedBox(height: 24),

            // Tâches à effectuer
            if (widget.mission.taches.isNotEmpty)
              _buildSection(
                'Tâches à effectuer (${widget.mission.taches.length})',
                widget.mission.taches
                    .map(
                      (tache) => Container(
                        margin: EdgeInsets.only(bottom: 8),
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_box_outline_blank,
                              size: 20,
                              color: Colors.grey,
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                tache,
                                style: TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                    .toList(),
              ),

            SizedBox(height: 24),

            // Notes du commercial
            if (widget.mission.notes?.isNotEmpty == true)
              _buildSection('Notes du commercial', [
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Text(
                    widget.mission.notes!,
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.5,
                      color: Colors.blue.shade800,
                    ),
                  ),
                ),
              ]),

            SizedBox(height: 32),

            // Boutons d'action
            if (!widget.mission.estTerminee) ...[
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _creerRapport(),
                      icon: Icon(Icons.description),
                      label: Text('Créer rapport'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed:
                          widget.mission.statut == 'en_attente'
                              ? () => _changerStatut('en_cours')
                              : widget.mission.statut == 'en_cours'
                              ? () => _changerStatut('terminee')
                              : null,
                      icon: Icon(
                        widget.mission.statut == 'en_attente'
                            ? Icons.play_arrow
                            : Icons.check,
                      ),
                      label: Text(
                        widget.mission.statut == 'en_attente'
                            ? 'Commencer'
                            : 'Terminer',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            widget.mission.statut == 'en_attente'
                                ? Colors.blue
                                : Colors.green,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green),
                    SizedBox(width: 12),
                    Text(
                      'Mission terminée',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade800,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800,
          ),
        ),
        SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey.shade600),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
                SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _changerStatut(String nouveauStatut) {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );

    missionProvider.mettreAJourStatut(widget.mission.id, nouveauStatut).then((
      success,
    ) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Statut mis à jour avec succès'),
            backgroundColor: Colors.green,
          ),
        );

        // Mettre à jour l'état local
        setState(() {
          // Le widget sera reconstruit avec les nouvelles données du provider
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la mise à jour'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  void _creerRapport() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreerRapportScreen(mission: widget.mission),
      ),
    );
  }

  IconData _getPrioriteIcon(String priorite) {
    switch (priorite) {
      case 'faible':
        return Icons.arrow_downward;
      case 'normale':
        return Icons.remove;
      case 'haute':
        return Icons.arrow_upward;
      case 'urgente':
        return Icons.priority_high;
      default:
        return Icons.remove;
    }
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'en_attente':
        return Colors.orange;
      case 'en_cours':
        return Colors.blue;
      case 'terminee':
        return Colors.green;
      case 'annulee':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
