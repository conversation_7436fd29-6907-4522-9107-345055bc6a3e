import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/mission_provider.dart';
import '../../providers/merchandiser_provider.dart';
import '../../providers/magasin_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/mission.dart';
import '../../models/merchandiser.dart';
import '../../models/magasin.dart';

class CreerMissionScreen extends StatefulWidget {
  @override
  _CreerMissionScreenState createState() => _CreerMissionScreenState();
}

class _CreerMissionScreenState extends State<CreerMissionScreen> {
  final _formKey = GlobalKey<FormState>();

  // Contrôleurs de texte
  final _titreController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();

  // État du formulaire
  String? _merchandiserSelectionne;
  String? _magasinSelectionne;
  DateTime _dateEcheance = DateTime.now().add(Duration(days: 1));
  String _priorite = 'normale';
  List<String> _taches = [];
  final _tacheController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _chargerDonnees();
  }

  void _chargerDonnees() {
    final merchandiserProvider = Provider.of<MerchandiserProvider>(
      context,
      listen: false,
    );
    final magasinProvider = Provider.of<MagasinProvider>(
      context,
      listen: false,
    );

    // Charger les merchandisers et magasins
    merchandiserProvider.chargerMerchandisers();
    magasinProvider.chargerMagasins();
  }

  @override
  void dispose() {
    _titreController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    _tacheController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Créer une mission'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Titre
              _buildSection('Titre de la mission', [
                TextFormField(
                  controller: _titreController,
                  decoration: InputDecoration(
                    hintText: 'Ex: Réaménagement présentoir VitaBrosse',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value?.isEmpty == true) {
                      return 'Veuillez entrer un titre';
                    }
                    return null;
                  },
                ),
              ]),

              SizedBox(height: 24),

              // Description
              _buildSection('Description', [
                TextFormField(
                  controller: _descriptionController,
                  maxLines: 4,
                  decoration: InputDecoration(
                    hintText: 'Décrivez la mission en détail...',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value?.isEmpty == true) {
                      return 'Veuillez entrer une description';
                    }
                    return null;
                  },
                ),
              ]),

              SizedBox(height: 24),

              // Merchandiser
              _buildSection('Merchandiser assigné', [
                Consumer<MerchandiserProvider>(
                  builder: (context, provider, child) {
                    if (provider.isLoading) {
                      return Center(child: CircularProgressIndicator());
                    }

                    return DropdownButtonFormField<String>(
                      value: _merchandiserSelectionne,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Sélectionnez un merchandiser',
                      ),
                      items:
                          provider.merchandisers.map((merchandiser) {
                            return DropdownMenuItem(
                              value: merchandiser.id.toString(),
                              child: Text(
                                '${merchandiser.prenom} ${merchandiser.nom}',
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _merchandiserSelectionne = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Veuillez sélectionner un merchandiser';
                        }
                        return null;
                      },
                    );
                  },
                ),
              ]),

              SizedBox(height: 24),

              // Magasin
              _buildSection('Magasin', [
                Consumer<MagasinProvider>(
                  builder: (context, provider, child) {
                    if (provider.isLoading) {
                      return Center(child: CircularProgressIndicator());
                    }

                    return DropdownButtonFormField<String>(
                      value: _magasinSelectionne,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Sélectionnez un magasin',
                      ),
                      items:
                          provider.magasins.map((magasin) {
                            return DropdownMenuItem(
                              value: magasin.id.toString(),
                              child: Text('${magasin.nom} - ${magasin.ville}'),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _magasinSelectionne = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Veuillez sélectionner un magasin';
                        }
                        return null;
                      },
                    );
                  },
                ),
              ]),

              SizedBox(height: 24),

              // Date d'échéance
              _buildSection('Date d\'échéance', [
                InkWell(
                  onTap: _selectionnerDate,
                  child: Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.calendar_today, color: Colors.grey.shade600),
                        SizedBox(width: 12),
                        Text(
                          '${_dateEcheance.day}/${_dateEcheance.month}/${_dateEcheance.year}',
                          style: TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                ),
              ]),

              SizedBox(height: 24),

              // Priorité
              _buildSection('Priorité', [
                DropdownButtonFormField<String>(
                  value: _priorite,
                  decoration: InputDecoration(border: OutlineInputBorder()),
                  items: [
                    DropdownMenuItem(value: 'faible', child: Text('Faible')),
                    DropdownMenuItem(value: 'normale', child: Text('Normale')),
                    DropdownMenuItem(value: 'haute', child: Text('Haute')),
                    DropdownMenuItem(value: 'urgente', child: Text('Urgente')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _priorite = value!;
                    });
                  },
                ),
              ]),

              SizedBox(height: 24),

              // Tâches
              _buildSection('Tâches à effectuer', [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _tacheController,
                        decoration: InputDecoration(
                          hintText: 'Ajouter une tâche...',
                          border: OutlineInputBorder(),
                        ),
                        onFieldSubmitted: (value) => _ajouterTache(),
                      ),
                    ),
                    SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _ajouterTache,
                      child: Text('Ajouter'),
                    ),
                  ],
                ),
                SizedBox(height: 12),
                ..._taches.map((tache) => _buildTacheItem(tache)),
              ]),

              SizedBox(height: 24),

              // Notes
              _buildSection('Notes (optionnel)', [
                TextFormField(
                  controller: _notesController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: 'Notes supplémentaires pour le merchandiser...',
                    border: OutlineInputBorder(),
                  ),
                ),
              ]),

              SizedBox(height: 32),

              // Bouton de création
              Container(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _creerMission,
                  child: Text('Créer la mission'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 16),
                    textStyle: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800,
          ),
        ),
        SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildTacheItem(String tache) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.task_alt, size: 20, color: Colors.blue),
          SizedBox(width: 12),
          Expanded(child: Text(tache, style: TextStyle(fontSize: 14))),
          IconButton(
            onPressed: () {
              setState(() {
                _taches.remove(tache);
              });
            },
            icon: Icon(Icons.close, color: Colors.red),
            constraints: BoxConstraints(minWidth: 32, minHeight: 32),
            padding: EdgeInsets.all(4),
          ),
        ],
      ),
    );
  }

  void _selectionnerDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dateEcheance,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 365)),
    );

    if (picked != null && picked != _dateEcheance) {
      setState(() {
        _dateEcheance = picked;
      });
    }
  }

  void _ajouterTache() {
    if (_tacheController.text.isNotEmpty) {
      setState(() {
        _taches.add(_tacheController.text);
        _tacheController.clear();
      });
    }
  }

  void _creerMission() {
    if (_formKey.currentState!.validate()) {
      final missionProvider = Provider.of<MissionProvider>(
        context,
        listen: false,
      );
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final magasinProvider = Provider.of<MagasinProvider>(
        context,
        listen: false,
      );

      // Trouver le nom du magasin
      final magasin = magasinProvider.magasins.firstWhere(
        (m) => m.id.toString() == _magasinSelectionne,
        orElse:
            () => Magasin(
              id: null,
              nom: 'Magasin inconnu',
              adresse: '',
              ville: '',
              codePostal: '',
              telephone: '',
              email: '',
              typeCommerce: 'commerce',
              latitude: 0.0,
              longitude: 0.0,
              dateCreation: DateTime.now(),
            ),
      );

      // Simuler un ID de commercial - vous devrez adapter selon votre AuthProvider
      final commercialId = 'commercial_123';

      final mission = Mission(
        id: missionProvider.genererIdMission(),
        titre: _titreController.text,
        description: _descriptionController.text,
        magasinId: _magasinSelectionne!,
        magasinNom: magasin.nom,
        merchandiserId: _merchandiserSelectionne!,
        commercialId: commercialId,
        dateCreation: DateTime.now(),
        dateEcheance: _dateEcheance,
        priorite: _priorite,
        taches: _taches,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      missionProvider.creerMission(mission).then((success) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Mission créée avec succès'),
              backgroundColor: Colors.green,
            ),
          );

          Navigator.pop(context);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la création de la mission'),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
    }
  }
}
