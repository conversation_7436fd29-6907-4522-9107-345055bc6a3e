import 'package:flutter/material.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'signup_unified_new_screen.dart';

class UserTypeSelectionScreen extends StatelessWidget {
  const UserTypeSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final isDesktop = size.width > 1200;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal:
                  isDesktop
                      ? size.width * 0.3
                      : isTablet
                      ? size.width * 0.2
                      : 24.0,
              vertical: 24.0,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo et titre
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical:
                        isDesktop
                            ? 40
                            : isTablet
                            ? 32
                            : 24,
                  ),
                  child: <PERSON>umn(
                    children: [
                      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
                        height:
                            isDesktop
                                ? 120
                                : isTablet
                                ? 100
                                : 80,
                      ),
                      SizedBox(height: 24),
                      Text(
                        'Créer un compte',
                        style: TextStyle(
                          fontSize:
                              isDesktop
                                  ? 32
                                  : isTablet
                                  ? 28
                                  : 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Choisissez le type de compte à créer',
                        style: TextStyle(
                          fontSize:
                              isDesktop
                                  ? 18
                                  : isTablet
                                  ? 16
                                  : 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),

                // Cartes de sélection
                Container(
                  constraints: BoxConstraints(
                    maxWidth: isDesktop ? 600 : double.infinity,
                  ),
                  child: Column(
                    children: [
                      // Carte Commercial
                      _buildUserTypeCard(
                        context: context,
                        title: 'Commercial',
                        subtitle: 'Gestion des clients, commandes et missions',
                        icon: Icons.business_center,
                        color: Colors.blue,
                        features: [
                          'Gérer les clients et prospects',
                          'Créer des devis et commandes',
                          'Assigner des missions aux merchandisers',
                          'Suivre les rapports de terrain',
                        ],
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SignupUnifiedNewScreen(userType: 'commercial'),
                            ),
                          );
                        },
                      ),

                      SizedBox(height: 24),

                      // Carte Merchandiser
                      _buildUserTypeCard(
                        context: context,
                        title: 'Merchandiser',
                        subtitle: 'Exécution des missions sur le terrain',
                        icon: Icons.store,
                        color: Colors.green,
                        features: [
                          'Recevoir des missions quotidiennes',
                          'Effectuer des visites magasins',
                          'Créer des rapports de visite',
                          'Prendre des photos et observations',
                        ],
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SignupUnifiedNewScreen(userType: 'merchandiser'),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 32),

                // Retour à la connexion
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    'Déjà un compte ? Se connecter',
                    style: TextStyle(color: Colors.blue, fontSize: 16),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserTypeCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required List<String> features,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(icon, size: 32, color: color),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(Icons.arrow_forward_ios, color: Colors.grey.shade400),
                ],
              ),

              SizedBox(height: 16),

              // Fonctionnalités
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children:
                    features.map((feature) {
                      return Padding(
                        padding: EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(Icons.check_circle, size: 16, color: color),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                feature,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
