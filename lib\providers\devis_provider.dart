import 'package:flutter/foundation.dart';

class DevisProvider with ChangeNotifier {
  List<Map<String, dynamic>> _devis = [];
  bool _isLoading = false;
  String _errorMessage = '';

  List<Map<String, dynamic>> get devis => _devis;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  Future<void> chargerDevis() async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      // Données exemple pour les devis
      _devis = [
        {
          'id': '1',
          'numero': 'DEV-2024-001',
          'clientId': 'client1',
          'dateCreation': DateTime.now(),
          'statut': 'brouillon',
          'montantTotal': 1500.0,
        },
        {
          'id': '2',
          'numero': 'DEV-2024-002',
          'clientId': 'client2',
          'dateCreation': DateTime.now(),
          'statut': 'accepte',
          'montantTotal': 2500.0,
        },
      ];
    } catch (e) {
      _errorMessage = 'Erreur lors du chargement des devis: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> ajouterDevis(Map<String, dynamic> devis) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      devis['id'] = DateTime.now().millisecondsSinceEpoch.toString();
      devis['dateCreation'] = DateTime.now();
      _devis.add(devis);
    } catch (e) {
      _errorMessage = 'Erreur lors de l\'ajout du devis: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> supprimerDevis(String id) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      _devis.removeWhere((d) => d['id'] == id);
    } catch (e) {
      _errorMessage = 'Erreur lors de la suppression du devis: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  List<Map<String, dynamic>> rechercherDevis(String terme) {
    if (terme.isEmpty) return _devis;

    return _devis.where((devis) {
      return devis['numero'].toLowerCase().contains(terme.toLowerCase()) ||
          devis['clientId'].toLowerCase().contains(terme.toLowerCase()) ||
          devis['statut'].toLowerCase().contains(terme.toLowerCase());
    }).toList();
  }

  double obtenirMontantTotal() {
    return _devis.fold(
      0.0,
      (total, devis) => total + (devis['montantTotal'] ?? 0.0),
    );
  }

  String genererNouveauNumero() {
    final maintenant = DateTime.now();
    final annee = maintenant.year.toString();
    final mois = maintenant.month.toString().padLeft(2, '0');
    final jour = maintenant.day.toString().padLeft(2, '0');

    final numero = (_devis.length + 1).toString().padLeft(3, '0');

    return 'DEV-$annee$mois$jour-$numero';
  }
}
