import 'package:cloud_firestore/cloud_firestore.dart';

class Client {
  final String? id; // ID Firebase (String)
  final String nom;
  final String prenom;
  final String email;
  final String telephone;
  final String adresse;
  final DateTime dateCreation;
  final String? codeClient;
  final String? matriculeFiscal;
  final String? categorie;
  final String? modeReglement;

  Client({
    this.id,
    required this.nom,
    required this.prenom,
    required this.email,
    required this.telephone,
    required this.adresse,
    required this.dateCreation,
    this.codeClient,
    this.matriculeFiscal,
    this.categorie,
    this.modeReglement,
  });

  // Convertir un Client en Map pour Firebase
  Map<String, dynamic> toFirestore() {
    return {
      'nom': nom,
      'prenom': prenom,
      'email': email,
      'telephone': telephone,
      'adresse': adresse,
      'dateCreation': Timestamp.fromDate(dateCreation),
      'codeClient': codeClient,
      'matriculeFiscal': matriculeFiscal,
      'categorie': categorie,
      'modeReglement': modeReglement,
    };
  }

  // Créer un Client à partir d'un document Firebase
  factory Client.fromFirestore(String docId, Map<String, dynamic> data) {
    return Client(
      id: docId,
      nom: data['nom'] ?? '',
      prenom: data['prenom'] ?? '',
      email: data['email'] ?? '',
      telephone: data['telephone'] ?? '',
      adresse: data['adresse'] ?? '',
      dateCreation:
          data['dateCreation'] is Timestamp
              ? (data['dateCreation'] as Timestamp).toDate()
              : DateTime.now(),
      codeClient: data['codeClient'],
      matriculeFiscal: data['matriculeFiscal'],
      categorie: data['categorie'],
      modeReglement: data['modeReglement'],
    );
  }

  // Convertir un Client en Map pour affichage ou stockage local
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nom': nom,
      'prenom': prenom,
      'email': email,
      'telephone': telephone,
      'adresse': adresse,
      'dateCreation': dateCreation.toIso8601String(),
      'codeClient': codeClient,
      'matriculeFiscal': matriculeFiscal,
      'categorie': categorie,
      'modeReglement': modeReglement,
    };
  }

  // Créer un Client à partir d'un Map
  factory Client.fromMap(Map<String, dynamic> map) {
    return Client(
      id: map['id']?.toString(),
      nom: map['nom'] ?? '',
      prenom: map['prenom'] ?? '',
      email: map['email'] ?? '',
      telephone: map['telephone'] ?? '',
      adresse: map['adresse'] ?? '',
      dateCreation:
          map['dateCreation'] is String
              ? DateTime.parse(map['dateCreation'])
              : (map['dateCreation'] as DateTime? ?? DateTime.now()),
      codeClient: map['codeClient'],
      matriculeFiscal: map['matriculeFiscal'],
      categorie: map['categorie'],
      modeReglement: map['modeReglement'],
    );
  }

  // Méthode copyWith pour créer une copie modifiée
  Client copyWith({
    String? id,
    String? nom,
    String? prenom,
    String? email,
    String? telephone,
    String? adresse,
    DateTime? dateCreation,
    String? codeClient,
    String? matriculeFiscal,
    String? categorie,
    String? modeReglement,
  }) {
    return Client(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      email: email ?? this.email,
      telephone: telephone ?? this.telephone,
      adresse: adresse ?? this.adresse,
      dateCreation: dateCreation ?? this.dateCreation,
      codeClient: codeClient ?? this.codeClient,
      matriculeFiscal: matriculeFiscal ?? this.matriculeFiscal,
      categorie: categorie ?? this.categorie,
      modeReglement: modeReglement ?? this.modeReglement,
    );
  }

  // Getter pour le nom complet
  String get nomComplet => '$prenom $nom';

  @override
  String toString() {
    return 'Client{id: $id, nom: $nom, prenom: $prenom, email: $email}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Client && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
