import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/tache_merchandising.dart';
import '../../providers/tache_merchandising_provider.dart';
import '../../providers/merchandiser_provider.dart';
import '../../providers/magasin_provider.dart';
import 'ajouter_tache_screen.dart';

class DetailTacheScreen extends StatefulWidget {
  final TacheMerchandising tache;

  const DetailTacheScreen({super.key, required this.tache});

  @override
  State<DetailTacheScreen> createState() => _DetailTacheScreenState();
}

class _DetailTacheScreenState extends State<DetailTacheScreen> {
  late TacheMerchandising tache;

  @override
  void initState() {
    super.initState();
    tache = widget.tache;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails de la tâche'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _modifierTache(context),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleAction(value),
            itemBuilder: (context) => _buildMenuItems(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTacheHeader(),
            const SizedBox(height: 24),
            _buildTacheDetails(),
            const SizedBox(height: 24),
            _buildTacheInfos(),
            const SizedBox(height: 24),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildTacheHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getColorFromHex(tache.couleurPriorite),
                  child: Icon(
                    _getIconFromString(tache.iconeType),
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tache.titre,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        tache.description,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildChip(
                  _getTypeLabel(tache.type),
                  Colors.blue,
                  Icons.category,
                ),
                _buildChip(
                  _getStatutLabel(tache.statut),
                  _getStatutColor(tache.statut),
                  _getStatutIcon(tache.statut),
                ),
                _buildChip(
                  _getPrioriteLabel(tache.priorite),
                  _getColorFromHex(tache.couleurPriorite),
                  _getPrioriteIcon(tache.priorite),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTacheDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Détails',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
              'Date d\'échéance',
              '${tache.dateEcheance.day}/${tache.dateEcheance.month}/${tache.dateEcheance.year}',
              Icons.calendar_today,
            ),
            if (tache.heureDebut != null)
              _buildDetailRow(
                'Heure de début',
                '${tache.heureDebut!.hour.toString().padLeft(2, '0')}:${tache.heureDebut!.minute.toString().padLeft(2, '0')}',
                Icons.access_time,
              ),
            if (tache.dureeEstimee != null)
              _buildDetailRow(
                'Durée estimée',
                '${tache.dureeEstimee!.inHours}h ${tache.dureeEstimee!.inMinutes % 60}min',
                Icons.timer,
              ),
            if (tache.dureeReelle != null)
              _buildDetailRow(
                'Durée réelle',
                '${tache.dureeReelle!.inHours}h ${tache.dureeReelle!.inMinutes % 60}min',
                Icons.timer_outlined,
              ),
            if (tache.estEnRetard)
              _buildDetailRow(
                'Statut',
                'En retard',
                Icons.warning,
                color: Colors.red,
              ),
            if (tache.rappel)
              _buildDetailRow(
                'Rappel',
                tache.dateRappel != null
                    ? '${tache.dateRappel!.day}/${tache.dateRappel!.month}/${tache.dateRappel!.year}'
                    : 'Activé',
                Icons.notification_important,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTacheInfos() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informations',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Consumer<MerchandiserProvider>(
              builder: (context, provider, child) {
                final merchandiser = provider.obtenirMerchandiserParId(
                  tache.merchandiserId,
                );
                return _buildDetailRow(
                  'Merchandiser',
                  merchandiser != null
                      ? '${merchandiser.nom} ${merchandiser.prenom}'
                      : 'Non trouvé',
                  Icons.person,
                );
              },
            ),
            if (tache.magasinId != null)
              Consumer<MagasinProvider>(
                builder: (context, provider, child) {
                  final magasin = provider.obtenirMagasinParId(
                    tache.magasinId!,
                  );
                  return _buildDetailRow(
                    'Magasin',
                    magasin?.nom ?? 'Non trouvé',
                    Icons.store,
                  );
                },
              ),
            _buildDetailRow(
              'Date de création',
              '${tache.dateCreation.day}/${tache.dateCreation.month}/${tache.dateCreation.year}',
              Icons.create,
            ),
            if (tache.dateRealisation != null)
              _buildDetailRow(
                'Date de réalisation',
                '${tache.dateRealisation!.day}/${tache.dateRealisation!.month}/${tache.dateRealisation!.year}',
                Icons.check_circle,
              ),
            if (tache.commentaires != null && tache.commentaires!.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  const Text(
                    'Commentaires',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(tache.commentaires!),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Actions',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: _buildActionButtons(),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildActionButtons() {
    final buttons = <Widget>[];

    if (tache.statut == StatutTache.planifiee) {
      buttons.add(
        ElevatedButton.icon(
          onPressed: () => _demarrerTache(),
          icon: const Icon(Icons.play_arrow),
          label: const Text('Démarrer'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
        ),
      );
    }

    if (tache.statut == StatutTache.en_cours) {
      buttons.add(
        ElevatedButton.icon(
          onPressed: () => _terminerTache(),
          icon: const Icon(Icons.check_circle),
          label: const Text('Terminer'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
        ),
      );
    }

    if (tache.statut != StatutTache.terminee &&
        tache.statut != StatutTache.annulee) {
      buttons.add(
        ElevatedButton.icon(
          onPressed: () => _reporterTache(),
          icon: const Icon(Icons.schedule),
          label: const Text('Reporter'),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
        ),
      );
    }

    return buttons;
  }

  Widget _buildChip(String label, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: color ?? Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<PopupMenuEntry<String>> _buildMenuItems() {
    final items = <PopupMenuEntry<String>>[];

    if (tache.statut == StatutTache.planifiee) {
      items.add(
        const PopupMenuItem(
          value: 'demarrer',
          child: Row(
            children: [
              Icon(Icons.play_arrow, color: Colors.green),
              SizedBox(width: 8),
              Text('Démarrer'),
            ],
          ),
        ),
      );
    }

    if (tache.statut == StatutTache.en_cours) {
      items.add(
        const PopupMenuItem(
          value: 'terminer',
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.blue),
              SizedBox(width: 8),
              Text('Terminer'),
            ],
          ),
        ),
      );
    }

    if (tache.statut != StatutTache.terminee &&
        tache.statut != StatutTache.annulee) {
      items.add(
        const PopupMenuItem(
          value: 'reporter',
          child: Row(
            children: [
              Icon(Icons.schedule, color: Colors.orange),
              SizedBox(width: 8),
              Text('Reporter'),
            ],
          ),
        ),
      );
    }

    items.addAll([
      const PopupMenuItem(
        value: 'modifier',
        child: Row(
          children: [
            Icon(Icons.edit, color: Colors.blue),
            SizedBox(width: 8),
            Text('Modifier'),
          ],
        ),
      ),
      const PopupMenuItem(
        value: 'supprimer',
        child: Row(
          children: [
            Icon(Icons.delete, color: Colors.red),
            SizedBox(width: 8),
            Text('Supprimer'),
          ],
        ),
      ),
    ]);

    return items;
  }

  void _handleAction(String action) {
    switch (action) {
      case 'demarrer':
        _demarrerTache();
        break;
      case 'terminer':
        _terminerTache();
        break;
      case 'reporter':
        _reporterTache();
        break;
      case 'modifier':
        _modifierTache(context);
        break;
      case 'supprimer':
        _supprimerTache();
        break;
    }
  }

  void _demarrerTache() {
    final provider = Provider.of<TacheMerchandisingProvider>(
      context,
      listen: false,
    );
    provider.demarrerTache(tache.id!).then((success) {
      if (success) {
        setState(() {
          tache = provider.obtenirTacheParId(tache.id!)!;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tâche démarrée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  void _terminerTache() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Terminer la tâche'),
            content: const Text(
              'Êtes-vous sûr de vouloir marquer cette tâche comme terminée ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  final provider = Provider.of<TacheMerchandisingProvider>(
                    context,
                    listen: false,
                  );
                  provider.terminerTache(tache.id!).then((success) {
                    if (success) {
                      setState(() {
                        tache = provider.obtenirTacheParId(tache.id!)!;
                      });
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Tâche terminée avec succès'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  });
                },
                child: const Text('Terminer'),
              ),
            ],
          ),
    );
  }

  void _reporterTache() {
    showDatePicker(
      context: context,
      initialDate: tache.dateEcheance.add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    ).then((nouvelleDate) {
      if (nouvelleDate != null) {
        final provider = Provider.of<TacheMerchandisingProvider>(
          context,
          listen: false,
        );
        provider.reporterTache(tache.id!, nouvelleDate).then((success) {
          if (success) {
            setState(() {
              tache = provider.obtenirTacheParId(tache.id!)!;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Tâche reportée avec succès'),
                backgroundColor: Colors.green,
              ),
            );
          }
        });
      }
    });
  }

  void _modifierTache(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => AjouterTacheScreen(tache: tache)),
    ).then((result) {
      if (result == true) {
        // Recharger la tâche modifiée
        final provider = Provider.of<TacheMerchandisingProvider>(
          context,
          listen: false,
        );
        final tacheModifiee = provider.obtenirTacheParId(tache.id!);
        if (tacheModifiee != null) {
          setState(() {
            tache = tacheModifiee;
          });
        }
      }
    });
  }

  void _supprimerTache() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Supprimer la tâche'),
            content: const Text(
              'Êtes-vous sûr de vouloir supprimer cette tâche ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  final provider = Provider.of<TacheMerchandisingProvider>(
                    context,
                    listen: false,
                  );
                  provider.supprimerTache(tache.id!).then((success) {
                    if (success) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Tâche supprimée avec succès'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  });
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }

  // Méthodes utilitaires
  Color _getColorFromHex(String hex) {
    return Color(int.parse(hex.replaceFirst('#', '0xFF')));
  }

  IconData _getIconFromString(String iconName) {
    switch (iconName) {
      case 'location_on':
        return Icons.location_on;
      case 'task_alt':
        return Icons.task_alt;
      case 'school':
        return Icons.school;
      case 'inventory':
        return Icons.inventory;
      case 'campaign':
        return Icons.campaign;
      case 'build':
        return Icons.build;
      default:
        return Icons.more_horiz;
    }
  }

  String _getTypeLabel(TypeTache type) {
    switch (type) {
      case TypeTache.visite:
        return 'Visite';
      case TypeTache.action:
        return 'Action';
      case TypeTache.formation:
        return 'Formation';
      case TypeTache.inventaire:
        return 'Inventaire';
      case TypeTache.promotion:
        return 'Promotion';
      case TypeTache.maintenance:
        return 'Maintenance';
      case TypeTache.autre:
        return 'Autre';
    }
  }

  String _getStatutLabel(StatutTache statut) {
    switch (statut) {
      case StatutTache.planifiee:
        return 'Planifiée';
      case StatutTache.en_cours:
        return 'En cours';
      case StatutTache.terminee:
        return 'Terminée';
      case StatutTache.reportee:
        return 'Reportée';
      case StatutTache.annulee:
        return 'Annulée';
    }
  }

  String _getPrioriteLabel(PrioriteTache priorite) {
    switch (priorite) {
      case PrioriteTache.basse:
        return 'Basse';
      case PrioriteTache.normale:
        return 'Normale';
      case PrioriteTache.haute:
        return 'Haute';
      case PrioriteTache.urgente:
        return 'Urgente';
    }
  }

  Color _getStatutColor(StatutTache statut) {
    switch (statut) {
      case StatutTache.planifiee:
        return Colors.blue;
      case StatutTache.en_cours:
        return Colors.orange;
      case StatutTache.terminee:
        return Colors.green;
      case StatutTache.reportee:
        return Colors.amber;
      case StatutTache.annulee:
        return Colors.red;
    }
  }

  IconData _getStatutIcon(StatutTache statut) {
    switch (statut) {
      case StatutTache.planifiee:
        return Icons.schedule;
      case StatutTache.en_cours:
        return Icons.play_arrow;
      case StatutTache.terminee:
        return Icons.check_circle;
      case StatutTache.reportee:
        return Icons.update;
      case StatutTache.annulee:
        return Icons.cancel;
    }
  }

  IconData _getPrioriteIcon(PrioriteTache priorite) {
    switch (priorite) {
      case PrioriteTache.basse:
        return Icons.arrow_downward;
      case PrioriteTache.normale:
        return Icons.remove;
      case PrioriteTache.haute:
        return Icons.arrow_upward;
      case PrioriteTache.urgente:
        return Icons.priority_high;
    }
  }
}
