class Mission {
  final String id;
  final String titre;
  final String description;
  final String magasinId;
  final String magasinNom;
  final String merchandiserId;
  final String commercialId;
  final DateTime dateCreation;
  final DateTime dateEcheance;
  final String statut; // 'en_attente', 'en_cours', 'terminee', 'annulee'
  final String priorite; // 'faible', 'normale', 'haute', 'urgente'
  final List<String> taches; // Liste des tâches à effectuer
  final String? notes; // Notes du commercial
  final Map<String, dynamic>? parametres; // Paramètres spécifiques à la mission

  Mission({
    required this.id,
    required this.titre,
    required this.description,
    required this.magasinId,
    required this.magasinNom,
    required this.merchandiserId,
    required this.commercialId,
    required this.dateCreation,
    required this.dateEcheance,
    this.statut = 'en_attente',
    this.priorite = 'normale',
    this.taches = const [],
    this.notes,
    this.parametres,
  });

  // Convertir une Mission en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'titre': titre,
      'description': description,
      'magasinId': magasinId,
      'magasinNom': magasinNom,
      'merchandiserId': merchandiserId,
      'commercialId': commercialId,
      'dateCreation': dateCreation.toIso8601String(),
      'dateEcheance': dateEcheance.toIso8601String(),
      'statut': statut,
      'priorite': priorite,
      'taches': taches.join('|'), // Convertir la liste en string
      'notes': notes,
      'parametres': parametres != null ? parametres.toString() : null,
    };
  }

  // Créer une Mission à partir d'une Map de la base de données
  factory Mission.fromMap(Map<String, dynamic> map) {
    return Mission(
      id: map['id'],
      titre: map['titre'],
      description: map['description'],
      magasinId: map['magasinId'],
      magasinNom: map['magasinNom'],
      merchandiserId: map['merchandiserId'],
      commercialId: map['commercialId'],
      dateCreation: DateTime.parse(map['dateCreation']),
      dateEcheance: DateTime.parse(map['dateEcheance']),
      statut: map['statut'],
      priorite: map['priorite'],
      taches: map['taches']?.split('|') ?? [],
      notes: map['notes'],
      parametres:
          map['parametres'] != null
              ? Map<String, dynamic>.from(map['parametres'])
              : null,
    );
  }

  // Créer une copie de la mission avec des modifications
  Mission copyWith({
    String? titre,
    String? description,
    String? magasinId,
    String? magasinNom,
    String? merchandiserId,
    String? commercialId,
    DateTime? dateCreation,
    DateTime? dateEcheance,
    String? statut,
    String? priorite,
    List<String>? taches,
    String? notes,
    Map<String, dynamic>? parametres,
  }) {
    return Mission(
      id: id,
      titre: titre ?? this.titre,
      description: description ?? this.description,
      magasinId: magasinId ?? this.magasinId,
      magasinNom: magasinNom ?? this.magasinNom,
      merchandiserId: merchandiserId ?? this.merchandiserId,
      commercialId: commercialId ?? this.commercialId,
      dateCreation: dateCreation ?? this.dateCreation,
      dateEcheance: dateEcheance ?? this.dateEcheance,
      statut: statut ?? this.statut,
      priorite: priorite ?? this.priorite,
      taches: taches ?? this.taches,
      notes: notes ?? this.notes,
      parametres: parametres ?? this.parametres,
    );
  }

  // Méthodes utilitaires
  bool get estEnRetard =>
      DateTime.now().isAfter(dateEcheance) && statut != 'terminee';
  bool get estUrgente => priorite == 'urgente' || estEnRetard;
  bool get estTerminee => statut == 'terminee';
  bool get estEnCours => statut == 'en_cours';

  String get statutAffichage {
    switch (statut) {
      case 'en_attente':
        return 'En attente';
      case 'en_cours':
        return 'En cours';
      case 'terminee':
        return 'Terminée';
      case 'annulee':
        return 'Annulée';
      default:
        return statut;
    }
  }

  String get prioriteAffichage {
    switch (priorite) {
      case 'faible':
        return 'Faible';
      case 'normale':
        return 'Normale';
      case 'haute':
        return 'Haute';
      case 'urgente':
        return 'Urgente';
      default:
        return priorite;
    }
  }
}
