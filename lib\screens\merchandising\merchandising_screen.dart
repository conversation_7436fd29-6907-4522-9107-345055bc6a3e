import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/merchandiser_provider.dart';
import '../../providers/magasin_provider.dart';
import '../../providers/parcours_provider.dart';
import '../../providers/tache_merchandising_provider.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'calendrier_merchandising_screen.dart';

class MerchandisingScreen extends StatefulWidget {
  const MerchandisingScreen({super.key});

  @override
  State<MerchandisingScreen> createState() => _MerchandisingScreenState();
}

class _MerchandisingScreenState extends State<MerchandisingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerDonnees();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _chargerDonnees() async {
    final merchandiserProvider = Provider.of<MerchandiserProvider>(
      context,
      listen: false,
    );
    final magasinProvider = Provider.of<MagasinProvider>(
      context,
      listen: false,
    );
    final parcoursProvider = Provider.of<ParcoursProvider>(
      context,
      listen: false,
    );
    final tacheProvider = Provider.of<TacheMerchandisingProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      merchandiserProvider.chargerMerchandisers(),
      magasinProvider.chargerMagasins(),
      parcoursProvider.chargerParcours(),
      tacheProvider.chargerTaches(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: isSmallScreen ? 120 : 160,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 2,
              flexibleSpace: FlexibleSpaceBar(
                titlePadding: EdgeInsets.only(
                  left: padding,
                  bottom: isSmallScreen ? 50 : 60,
                ),
                title: Row(
                  children: [
                    const VitaBrosseLogo(height: 28, showText: false),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Merchandising',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: const Color(0xFF1F2937),
                              fontSize: isSmallScreen ? 18 : 22,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Gestion terrain et visites',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Colors.grey.shade600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        const Color(0xFF10B981).withValues(alpha: 0.08),
                        const Color(0xFF059669).withValues(alpha: 0.08),
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Décoration de fond adaptée
                      Positioned(
                        top: isSmallScreen ? 20 : 40,
                        right: isSmallScreen ? 10 : 20,
                        child: Container(
                          width: isSmallScreen ? 60 : 100,
                          height: isSmallScreen ? 60 : 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF10B981).withValues(alpha: 0.15),
                                const Color(0xFF059669).withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: isSmallScreen ? 30 : 60,
                        right: isSmallScreen ? 20 : 40,
                        child: Container(
                          width: isSmallScreen ? 40 : 60,
                          height: isSmallScreen ? 40 : 60,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF059669).withValues(alpha: 0.2),
                                const Color(0xFF10B981).withValues(alpha: 0.15),
                              ],
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.store,
                              size: isSmallScreen ? 20 : 28,
                              color: const Color(0xFF059669),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(48.0),
                child: Container(
                  color: Colors.white,
                  child: TabBar(
                    controller: _tabController,
                    indicatorColor: const Color(0xFF10B981),
                    indicatorWeight: 3,
                    labelColor: const Color(0xFF10B981),
                    unselectedLabelColor: Colors.grey.shade600,
                    labelStyle: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: isSmallScreen ? 12 : 14,
                    ),
                    unselectedLabelStyle: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: isSmallScreen ? 12 : 14,
                    ),
                    isScrollable: isSmallScreen,
                    tabs: [
                      Tab(text: isSmallScreen ? 'Vue' : 'Vue d\'ensemble'),
                      Tab(text: 'Merchandisers'),
                      Tab(text: 'Magasins'),
                      Tab(text: 'Parcours'),
                      Tab(text: 'Calendrier'),
                    ],
                  ),
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            MerchandisingDashboard(isSmallScreen: isSmallScreen),
            const MerchandisersTab(),
            const MagasinsTab(),
            const ParcoursTab(),
            Consumer<TacheMerchandisingProvider>(
              builder: (context, provider, child) {
                return const CalendrierMerchandisingScreen();
              },
            ),
          ],
        ),
      ),
    );
  }
}

class MerchandisingDashboard extends StatelessWidget {
  final bool isSmallScreen;

  const MerchandisingDashboard({super.key, required this.isSmallScreen});
  @override
  Widget build(BuildContext context) {
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Padding(
      padding: EdgeInsets.all(padding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Vue d\'ensemble',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
              fontSize: isSmallScreen ? 18 : 22,
            ),
          ),
          SizedBox(height: isSmallScreen ? 16 : 20),

          // Statistiques rapides adaptées au mobile
          Expanded(
            child: GridView.count(
              crossAxisCount: isSmallScreen ? 2 : 2,
              crossAxisSpacing: isSmallScreen ? 12 : 16,
              mainAxisSpacing: isSmallScreen ? 12 : 16,
              childAspectRatio: isSmallScreen ? 1.1 : 1.2,
              children: [
                _buildStatCard(
                  context,
                  'Merchandisers',
                  Icons.people_outline,
                  const Color(0xFF3B82F6),
                  Consumer<MerchandiserProvider>(
                    builder: (context, provider, child) {
                      return FutureBuilder<Map<String, dynamic>>(
                        future: provider.obtenirStatistiques(),
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return _buildCardContent(
                              '${snapshot.data!['nombreActifs']}',
                              'Actifs',
                              isSmallScreen,
                            );
                          }
                          return const CircularProgressIndicator();
                        },
                      );
                    },
                  ),
                  isSmallScreen,
                ),
                _buildStatCard(
                  context,
                  'Magasins',
                  Icons.store_outlined,
                  const Color(0xFF10B981),
                  Consumer<MagasinProvider>(
                    builder: (context, provider, child) {
                      return FutureBuilder<Map<String, dynamic>>(
                        future: provider.obtenirStatistiques(),
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return _buildCardContent(
                              '${snapshot.data!['nombreActifs']}',
                              'Actifs',
                              isSmallScreen,
                            );
                          }
                          return const CircularProgressIndicator();
                        },
                      );
                    },
                  ),
                  isSmallScreen,
                ),
                _buildStatCard(
                  context,
                  'Parcours du jour',
                  Icons.route_outlined,
                  const Color(0xFFF59E0B),
                  Consumer<ParcoursProvider>(
                    builder: (context, provider, child) {
                      final parcoursAujourdhui = provider
                          .obtenirParcoursParDate(DateTime.now());
                      return _buildCardContent(
                        '${parcoursAujourdhui.length}',
                        'Planifiés',
                        isSmallScreen,
                      );
                    },
                  ),
                  isSmallScreen,
                ),
                _buildStatCard(
                  context,
                  'En cours',
                  Icons.schedule_outlined,
                  const Color(0xFF8B5CF6),
                  Consumer<ParcoursProvider>(
                    builder: (context, provider, child) {
                      final parcoursEnCours = provider.obtenirParcoursParStatut(
                        'en_cours',
                      );
                      return _buildCardContent(
                        '${parcoursEnCours.length}',
                        'Parcours',
                        isSmallScreen,
                      );
                    },
                  ),
                  isSmallScreen,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    Widget valueWidget,
    bool isSmallScreen,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(isSmallScreen ? 16 : 20),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: isSmallScreen ? 16 : 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 10 : 12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, size: isSmallScreen ? 20 : 24, color: color),
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
          SizedBox(height: isSmallScreen ? 2 : 4),
          valueWidget,
        ],
      ),
    );
  }

  Widget _buildCardContent(String value, String subtitle, bool isSmallScreen) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: isSmallScreen ? 20 : 24,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF1F2937),
          ),
        ),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: isSmallScreen ? 10 : 12,
            color: Colors.grey.shade500,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

class MerchandisersTab extends StatelessWidget {
  const MerchandisersTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('Gestion des Merchandisers - À implémenter'),
    );
  }
}

class MagasinsTab extends StatelessWidget {
  const MagasinsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Gestion des Magasins - À implémenter'));
  }
}

class ParcoursTab extends StatelessWidget {
  const ParcoursTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Gestion des Parcours - À implémenter'));
  }
}
