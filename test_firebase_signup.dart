import 'package:firebase_core/firebase_core.dart';
import 'lib/firebase_options.dart';
import 'lib/providers/firebase_auth_provider.dart';
import 'lib/services/firebase_service.dart';

void main() async {
  print('🔥 Début du test Firebase signup...');
  
  try {
    // Initialiser Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('🔥 Firebase initialisé avec succès');

    // Initialiser Firestore
    await FirebaseService.initializeFirestore();
    print('🔥 Firestore initialisé avec succès');

    // Créer une instance du provider
    final authProvider = FirebaseAuthProvider();
    print('🔥 FirebaseAuthProvider créé');

    // Tester l'inscription d'un commercial
    print('🔥 Test inscription commercial...');
    final resultCommercial = await authProvider.signUpWithUserData(
      email: '<EMAIL>',
      password: 'password123',
      nomComplet: 'Test Commercial',
      telephone: '**********',
      userType: 'commercial',
      mobile: '**********',
      territoire: 'Paris',
      status: 'Inactif',
    );

    if (resultCommercial) {
      print('✅ Inscription commercial réussie!');
    } else {
      print('❌ Échec inscription commercial: ${authProvider.errorMessage}');
    }

    // Attendre un peu avant le test suivant
    await Future.delayed(Duration(seconds: 2));

    // Tester l'inscription d'un merchandiser
    print('🔥 Test inscription merchandiser...');
    final resultMerchandiser = await authProvider.signUpWithUserData(
      email: '<EMAIL>',
      password: 'password123',
      nomComplet: 'Test Merchandiser',
      telephone: '**********',
      userType: 'merchandiser',
      mobile: '**********',
      territoire: 'Lyon',
      status: 'Inactif',
    );

    if (resultMerchandiser) {
      print('✅ Inscription merchandiser réussie!');
    } else {
      print('❌ Échec inscription merchandiser: ${authProvider.errorMessage}');
    }

    print('🔥 Tests terminés!');
    
  } catch (e) {
    print('❌ Erreur lors du test: $e');
  }
}
