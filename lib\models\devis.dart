import 'devis_item.dart';

enum StatutDevis { brouillon, envoye, accepte, refuse, expire }

class Devis {
  final String? id;
  final String numero;
  final String clientId;
  final DateTime dateCreation;
  final DateTime dateExpiration;
  final String conditionsValidite;
  final StatutDevis statut;
  final List<DevisItem> items;
  final double remisePourcentage;
  final double remiseMontant;
  final double tauxTva;
  final String? notes;
  final String? contactCommercial;
  final DateTime? dateEnvoi;
  final DateTime? dateReponse;

  Devis({
    this.id,
    required this.numero,
    required this.clientId,
    required this.dateCreation,
    required this.dateExpiration,
    required this.conditionsValidite,
    this.statut = StatutDevis.brouillon,
    required this.items,
    this.remisePourcentage = 0.0,
    this.remiseMontant = 0.0,
    this.tauxTva = 20.0,
    this.notes,
    this.contactCommercial,
    this.dateEnvoi,
    this.dateReponse,
  });

  // Calculs
  double get sousTotal {
    return items.fold(0.0, (total, item) => total + item.sousTotal);
  }

  double get montantRemise {
    if (remiseMontant > 0) {
      return remiseMontant;
    }
    return sousTotal * (remisePourcentage / 100);
  }

  double get totalHT {
    return sousTotal - montantRemise;
  }

  double get montantTva {
    return totalHT * (tauxTva / 100);
  }

  double get totalTTC {
    return totalHT + montantTva;
  }

  int get nombreArticles {
    return items.fold(0, (total, item) => total + item.quantite);
  }

  // Formatage
  String get numeroFormate => 'DEV-$numero';
  String get sousTotalFormate => '${sousTotal.toStringAsFixed(2)} €';
  String get montantRemiseFormate => '${montantRemise.toStringAsFixed(2)} €';
  String get totalHTFormate => '${totalHT.toStringAsFixed(2)} €';
  String get montantTvaFormate => '${montantTva.toStringAsFixed(2)} €';
  String get totalTTCFormate => '${totalTTC.toStringAsFixed(2)} €';

  String get statutFormate {
    switch (statut) {
      case StatutDevis.brouillon:
        return 'Brouillon';
      case StatutDevis.envoye:
        return 'Envoyé';
      case StatutDevis.accepte:
        return 'Accepté';
      case StatutDevis.refuse:
        return 'Refusé';
      case StatutDevis.expire:
        return 'Expiré';
    }
  }

  bool get estExpire {
    return DateTime.now().isAfter(dateExpiration);
  }

  bool get peutEtreModifie {
    return statut == StatutDevis.brouillon;
  }

  bool get peutEtreEnvoye {
    return statut == StatutDevis.brouillon && !estExpire;
  }

  bool get peutEtreTransformeEnCommande {
    return statut == StatutDevis.accepte;
  }

  // Convertir un Devis en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'numero': numero,
      'clientId': clientId,
      'dateCreation': dateCreation.toIso8601String(),
      'dateExpiration': dateExpiration.toIso8601String(),
      'conditionsValidite': conditionsValidite,
      'statut': statut.index,
      'remisePourcentage': remisePourcentage,
      'remiseMontant': remiseMontant,
      'tauxTva': tauxTva,
      'notes': notes,
      'contactCommercial': contactCommercial,
      'dateEnvoi': dateEnvoi?.toIso8601String(),
      'dateReponse': dateReponse?.toIso8601String(),
    };
  }

  // Créer un Devis à partir d'une Map de la base de données
  factory Devis.fromMap(Map<String, dynamic> map, {List<DevisItem>? items}) {
    return Devis(
      id: map['id'],
      numero: map['numero'],
      clientId: map['clientId'],
      dateCreation: DateTime.parse(map['dateCreation']),
      dateExpiration: DateTime.parse(map['dateExpiration']),
      conditionsValidite: map['conditionsValidite'],
      statut: StatutDevis.values[map['statut']],
      items: items ?? [],
      remisePourcentage: map['remisePourcentage']?.toDouble() ?? 0.0,
      remiseMontant: map['remiseMontant']?.toDouble() ?? 0.0,
      tauxTva: map['tauxTva']?.toDouble() ?? 20.0,
      notes: map['notes'],
      contactCommercial: map['contactCommercial'],
      dateEnvoi:
          map['dateEnvoi'] != null ? DateTime.parse(map['dateEnvoi']) : null,
      dateReponse:
          map['dateReponse'] != null
              ? DateTime.parse(map['dateReponse'])
              : null,
    );
  }

  // Créer une copie du devis avec des modifications
  Devis copyWith({
    String? id,
    String? numero,
    String? clientId,
    DateTime? dateCreation,
    DateTime? dateExpiration,
    String? conditionsValidite,
    StatutDevis? statut,
    List<DevisItem>? items,
    double? remisePourcentage,
    double? remiseMontant,
    double? tauxTva,
    String? notes,
    String? contactCommercial,
    DateTime? dateEnvoi,
    DateTime? dateReponse,
  }) {
    return Devis(
      id: id ?? this.id,
      numero: numero ?? this.numero,
      clientId: clientId ?? this.clientId,
      dateCreation: dateCreation ?? this.dateCreation,
      dateExpiration: dateExpiration ?? this.dateExpiration,
      conditionsValidite: conditionsValidite ?? this.conditionsValidite,
      statut: statut ?? this.statut,
      items: items ?? this.items,
      remisePourcentage: remisePourcentage ?? this.remisePourcentage,
      remiseMontant: remiseMontant ?? this.remiseMontant,
      tauxTva: tauxTva ?? this.tauxTva,
      notes: notes ?? this.notes,
      contactCommercial: contactCommercial ?? this.contactCommercial,
      dateEnvoi: dateEnvoi ?? this.dateEnvoi,
      dateReponse: dateReponse ?? this.dateReponse,
    );
  }

  @override
  String toString() {
    return 'Devis{id: $id, numero: $numero, clientId: $clientId, statut: $statut}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Devis && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
