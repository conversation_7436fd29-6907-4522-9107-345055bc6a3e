import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/rapport_provider.dart';
import '../../models/mission.dart';
import '../../models/rapport.dart';

class CreerRapportScreen extends StatefulWidget {
  final Mission? mission;

  const CreerRapportScreen({super.key, this.mission});

  @override
  State<CreerRapportScreen> createState() => _CreerRapportScreenState();
}

class _CreerRapportScreenState extends State<CreerRapportScreen> {
  final _formKey = GlobalKey<FormState>();

  // Contrôleurs de texte
  final _commentairesController = TextEditingController();
  final _dureeVisiteController = TextEditingController();
  final _problemesController = TextEditingController();

  // État du formulaire
  final List<String> _tachesRealisees = [];
  final List<String> _tachesNonRealisees = [];
  final List<String> _photosUrls = [];
  DateTime _dateRapport = DateTime.now();

  @override
  void initState() {
    super.initState();

    // Si une mission est fournie, initialiser les tâches
    if (widget.mission != null) {
      // Toutes les tâches commencent comme non réalisées
      _tachesNonRealisees.addAll(widget.mission!.taches);
    }
  }

  @override
  void dispose() {
    _commentairesController.dispose();
    _dureeVisiteController.dispose();
    _problemesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final padding = isSmallScreen ? 16.0 : 24.0;

    return Scaffold(
      appBar: AppBar(
        title: Text('Nouveau rapport'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _sauvegarderBrouillon,
            icon: Icon(Icons.save),
            tooltip: 'Sauvegarder en brouillon',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(padding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Informations de la mission (si fournie)
              if (widget.mission != null) ...[
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Mission: ${widget.mission!.titre}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Magasin: ${widget.mission!.magasinNom}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 24),
              ],

              // Date et durée - Layout horizontal optimisé
              if (isSmallScreen) ...[
                // Sur petits écrans : vertical
                _buildSection('Date du rapport', [
                  InkWell(
                    onTap: _selectionnerDate,
                    child: Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            color: Colors.grey.shade600,
                          ),
                          SizedBox(width: 12),
                          Text(
                            '${_dateRapport.day}/${_dateRapport.month}/${_dateRapport.year}',
                            style: TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),
                ]),
                SizedBox(height: 24),
                _buildSection('Durée de la visite (heures)', [
                  TextFormField(
                    controller: _dureeVisiteController,
                    keyboardType: TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    decoration: InputDecoration(
                      hintText: 'Ex: 2.5',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.access_time),
                    ),
                    validator: (value) {
                      if (value?.isNotEmpty == true) {
                        final duree = double.tryParse(value!);
                        if (duree == null || duree <= 0) {
                          return 'Veuillez entrer une durée valide';
                        }
                      }
                      return null;
                    },
                  ),
                ]),
              ] else ...[
                // Sur grands écrans : horizontal
                _buildSection('Date et durée de la visite', [
                  Row(
                    children: [
                      // Date
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Date du rapport',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            SizedBox(height: 8),
                            InkWell(
                              onTap: _selectionnerDate,
                              child: Container(
                                padding: EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.calendar_today,
                                      color: Colors.grey.shade600,
                                    ),
                                    SizedBox(width: 12),
                                    Text(
                                      '${_dateRapport.day}/${_dateRapport.month}/${_dateRapport.year}',
                                      style: TextStyle(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 24),
                      // Durée
                      Expanded(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Durée (heures)',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            SizedBox(height: 8),
                            TextFormField(
                              controller: _dureeVisiteController,
                              keyboardType: TextInputType.numberWithOptions(
                                decimal: true,
                              ),
                              decoration: InputDecoration(
                                hintText: 'Ex: 2.5',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.access_time),
                              ),
                              validator: (value) {
                                if (value?.isNotEmpty == true) {
                                  final duree = double.tryParse(value!);
                                  if (duree == null || duree <= 0) {
                                    return 'Veuillez entrer une durée valide';
                                  }
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ]),
              ],

              SizedBox(height: 24),

              // Tâches réalisées/non réalisées
              if (widget.mission != null && widget.mission!.taches.isNotEmpty)
                _buildSection('Tâches de la mission', [
                  ...widget.mission!.taches.map(
                    (tache) => _buildTacheItem(tache),
                  ),
                ]),

              SizedBox(height: 24),

              // Commentaires et problèmes - Layout horizontal optimisé
              if (isSmallScreen) ...[
                // Sur petits écrans : vertical
                _buildSection('Commentaires et observations', [
                  TextFormField(
                    controller: _commentairesController,
                    maxLines: 5,
                    decoration: InputDecoration(
                      hintText:
                          'Décrivez votre visite, les observations importantes...',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value?.isEmpty == true) {
                        return 'Veuillez ajouter des commentaires';
                      }
                      return null;
                    },
                  ),
                ]),
                SizedBox(height: 24),
                _buildSection('Problèmes rencontrés', [
                  TextFormField(
                    controller: _problemesController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      hintText: 'Décrivez les problèmes rencontrés (optionnel)',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ]),
              ] else ...[
                // Sur grands écrans : côte à côte
                _buildSection('Commentaires et problèmes', [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Commentaires
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Commentaires et observations',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            SizedBox(height: 8),
                            TextFormField(
                              controller: _commentairesController,
                              maxLines: 5,
                              decoration: InputDecoration(
                                hintText:
                                    'Décrivez votre visite, les observations importantes...',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value?.isEmpty == true) {
                                  return 'Veuillez ajouter des commentaires';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 24),
                      // Problèmes
                      Expanded(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Problèmes rencontrés',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            SizedBox(height: 8),
                            TextFormField(
                              controller: _problemesController,
                              maxLines: 5, // Même hauteur que les commentaires
                              decoration: InputDecoration(
                                hintText:
                                    'Décrivez les problèmes rencontrés (optionnel)',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ]),
              ],

              SizedBox(height: 24),

              // Photos
              _buildSection('Photos', [
                Container(
                  width: double.infinity,
                  height: 120,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: InkWell(
                    onTap: _ajouterPhoto,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add_a_photo,
                          size: 40,
                          color: Colors.grey.shade600,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Ajouter des photos',
                          style: TextStyle(color: Colors.grey.shade600),
                        ),
                      ],
                    ),
                  ),
                ),
                if (_photosUrls.isNotEmpty) ...[
                  SizedBox(height: 12),
                  Text('${_photosUrls.length} photo(s) ajoutée(s)'),
                ],
              ]),

              SizedBox(height: 32),

              // Boutons d'action
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _sauvegarderBrouillon,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade600,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text('Sauvegarder en brouillon'),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _envoyerRapport,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text('Envoyer le rapport'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800,
          ),
        ),
        SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildTacheItem(String tache) {
    final isRealisee = _tachesRealisees.contains(tache);

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Checkbox(
            value: isRealisee,
            onChanged: (bool? value) {
              setState(() {
                if (value == true) {
                  _tachesRealisees.add(tache);
                  _tachesNonRealisees.remove(tache);
                } else {
                  _tachesRealisees.remove(tache);
                  _tachesNonRealisees.add(tache);
                }
              });
            },
          ),
          Expanded(
            child: Text(
              tache,
              style: TextStyle(
                fontSize: 14,
                decoration: isRealisee ? TextDecoration.lineThrough : null,
                color: isRealisee ? Colors.grey.shade600 : Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _selectionnerDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dateRapport,
      firstDate: DateTime.now().subtract(Duration(days: 30)),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _dateRapport) {
      setState(() {
        _dateRapport = picked;
      });
    }
  }

  void _ajouterPhoto() {
    // Simuler l'ajout d'une photo
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Fonctionnalité d\'ajout de photo à implémenter'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _sauvegarderBrouillon() {
    if (_formKey.currentState!.validate()) {
      _sauvegarderRapport('brouillon');
    }
  }

  void _envoyerRapport() {
    if (_formKey.currentState!.validate()) {
      _sauvegarderRapport('envoye');
    }
  }

  void _sauvegarderRapport(String statut) {
    final rapportProvider = Provider.of<RapportProvider>(
      context,
      listen: false,
    );

    // Simuler des IDs - vous devrez adapter selon votre AuthProvider
    final merchandiserId = 'merchandiser_123';
    final commercialId = widget.mission?.commercialId ?? 'commercial_123';

    final rapport = Rapport(
      id: rapportProvider.genererIdRapport(),
      missionId: widget.mission?.id ?? '',
      merchandiserId: merchandiserId,
      commercialId: commercialId,
      dateCreation: DateTime.now(),
      dateRapport: _dateRapport,
      statut: statut,
      commentaires: _commentairesController.text,
      photosUrls: _photosUrls,
      donnees: {},
      dureeVisite: double.tryParse(_dureeVisiteController.text),
      problemesDeclares:
          _problemesController.text.isNotEmpty
              ? _problemesController.text
              : null,
      tachesRealisees: _tachesRealisees,
      tachesNonRealisees: _tachesNonRealisees,
    );

    rapportProvider.creerRapport(rapport).then((success) {
      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              statut == 'brouillon'
                  ? 'Rapport sauvegardé en brouillon'
                  : 'Rapport envoyé avec succès',
            ),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.pop(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sauvegarde du rapport'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }
}
