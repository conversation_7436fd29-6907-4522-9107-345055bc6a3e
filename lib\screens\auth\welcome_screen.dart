import 'package:flutter/material.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'welcome_user_type_screen.dart';
import '../home_screen.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final isDesktop = size.width > 1200;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal:
                  isDesktop
                      ? size.width * 0.25
                      : isTablet
                      ? size.width * 0.15
                      : 24.0,
              vertical: 32.0,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo principal et titre
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical:
                        isDesktop
                            ? 80
                            : isTablet
                            ? 60
                            : 40,
                  ),
                  child: Column(
                    children: [
                      // Logo VitaBrosse
                      Container(
                        constraints: BoxConstraints(
                          maxWidth: MediaQuery.of(context).size.width * 0.9,
                        ),
                        child: <PERSON>(
                          tag: 'vitabrosse_logo',
                          child: VitaBrosseLogo(
                            height:
                                isDesktop
                                    ? 150
                                    : isTablet
                                    ? 120
                                    : 100,
                            showText: true,
                          ),
                        ),
                      ),
                      SizedBox(
                        height:
                            isDesktop
                                ? 40
                                : isTablet
                                ? 32
                                : 24,
                      ),

                      // Titre principal
                      Text(
                        'Bienvenue sur',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineLarge?.copyWith(
                          fontWeight: FontWeight.w300,
                          color: Colors.grey.shade600,
                          fontSize:
                              isDesktop
                                  ? 36
                                  : isTablet
                                  ? 32
                                  : 24,
                        ),
                      ),
                      SizedBox(height: isDesktop ? 12 : 8),

                      Text(
                        'VitaBrosse Pro',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF1F2937),
                          fontSize:
                              isDesktop
                                  ? 48
                                  : isTablet
                                  ? 40
                                  : 32,
                        ),
                      ),
                      SizedBox(
                        height:
                            isDesktop
                                ? 20
                                : isTablet
                                ? 16
                                : 12,
                      ),

                      Container(
                        constraints: BoxConstraints(
                          maxWidth:
                              isDesktop
                                  ? 600
                                  : isTablet
                                  ? 500
                                  : double.infinity,
                        ),
                        child: Text(
                          'Votre solution complète de gestion commerciale.\nSimplifiez vos commandes, clients et produits en toute simplicité.',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyLarge?.copyWith(
                            color: Colors.grey.shade600,
                            fontSize:
                                isDesktop
                                    ? 20
                                    : isTablet
                                    ? 18
                                    : 16,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),

                // Boutons d'action
                Container(
                  constraints: BoxConstraints(
                    maxWidth: isDesktop ? 400 : double.infinity,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Bouton Se connecter
                      FilledButton(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder:
                                  (context) => const WelcomeUserTypeScreen(),
                            ),
                          );
                        },
                        style: FilledButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            vertical: isDesktop ? 20 : 16,
                            horizontal: 32,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.login),
                            const SizedBox(width: 12),
                            Text(
                              'Se connecter',
                              style: TextStyle(
                                fontSize: isDesktop ? 18 : 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: isDesktop ? 20 : 16),

                      // Bouton Créer un compte
                      OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder:
                                  (context) => const WelcomeUserTypeScreen(),
                            ),
                          );
                        },
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            vertical: isDesktop ? 20 : 16,
                            horizontal: 32,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          side: BorderSide(
                            color: Theme.of(context).colorScheme.primary,
                            width: 2,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.person_add),
                            const SizedBox(width: 12),
                            Text(
                              'Créer un compte',
                              style: TextStyle(
                                fontSize: isDesktop ? 18 : 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: isDesktop ? 32 : 24),

                      // Divider avec texte
                      Row(
                        children: [
                          Expanded(child: Divider(color: Colors.grey.shade300)),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Text(
                              'ou',
                              style: TextStyle(
                                color: Colors.grey.shade500,
                                fontSize: isDesktop ? 16 : 14,
                              ),
                            ),
                          ),
                          Expanded(child: Divider(color: Colors.grey.shade300)),
                        ],
                      ),

                      SizedBox(height: isDesktop ? 24 : 20),

                      // Bouton Continuer en tant qu'invité
                      TextButton.icon(
                        onPressed: () {
                          Navigator.of(context).pushReplacement(
                            MaterialPageRoute(
                              builder: (context) => const HomeScreen(),
                            ),
                          );
                        },
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            vertical: isDesktop ? 16 : 12,
                            horizontal: 24,
                          ),
                        ),
                        icon: const Icon(Icons.person_outline),
                        label: Text(
                          'Continuer en tant qu\'invité',
                          style: TextStyle(
                            fontSize: isDesktop ? 16 : 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(
                  height:
                      isDesktop
                          ? 60
                          : isTablet
                          ? 40
                          : 32,
                ),

                // Points clés de l'application
                Container(
                  constraints: BoxConstraints(
                    maxWidth: isDesktop ? 800 : double.infinity,
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Pourquoi choisir VitaBrosse Pro ?',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2937),
                          fontSize:
                              isDesktop
                                  ? 24
                                  : isTablet
                                  ? 20
                                  : 18,
                        ),
                      ),
                      SizedBox(
                        height:
                            isDesktop
                                ? 32
                                : isTablet
                                ? 24
                                : 20,
                      ),

                      Row(
                        children: [
                          if (isDesktop) ...[
                            Expanded(
                              child: _buildFeatureCard(
                                context,
                                Icons.shopping_cart,
                                'Gestion des commandes',
                                'Créez et suivez vos commandes facilement',
                                isDesktop,
                              ),
                            ),
                            const SizedBox(width: 24),
                            Expanded(
                              child: _buildFeatureCard(
                                context,
                                Icons.people,
                                'Gestion des clients',
                                'Centralisez toutes vos données clients',
                                isDesktop,
                              ),
                            ),
                            const SizedBox(width: 24),
                            Expanded(
                              child: _buildFeatureCard(
                                context,
                                Icons.inventory,
                                'Gestion des produits',
                                'Organisez votre catalogue produits',
                                isDesktop,
                              ),
                            ),
                          ] else ...[
                            Expanded(
                              child: Column(
                                children: [
                                  _buildFeatureCard(
                                    context,
                                    Icons.shopping_cart,
                                    'Gestion des commandes',
                                    'Créez et suivez vos commandes facilement',
                                    isDesktop,
                                  ),
                                  const SizedBox(height: 16),
                                  _buildFeatureCard(
                                    context,
                                    Icons.people,
                                    'Gestion des clients',
                                    'Centralisez toutes vos données clients',
                                    isDesktop,
                                  ),
                                  const SizedBox(height: 16),
                                  _buildFeatureCard(
                                    context,
                                    Icons.inventory,
                                    'Gestion des produits',
                                    'Organisez votre catalogue produits',
                                    isDesktop,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context,
    IconData icon,
    String title,
    String description,
    bool isDesktop,
  ) {
    return Container(
      padding: EdgeInsets.all(isDesktop ? 24 : 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade100,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              size: isDesktop ? 32 : 24,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
              fontSize: isDesktop ? 18 : 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade600,
              fontSize: isDesktop ? 16 : 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
