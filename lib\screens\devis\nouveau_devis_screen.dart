import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../providers/produit_provider.dart';
import '../../models/client.dart';
import '../../models/produit.dart';
import '../../models/devis.dart';
import '../../models/devis_item.dart';
import '../../services/devis_service.dart';
import '../../main.dart' show DevisProvider;

class NouveauDevisScreen extends StatefulWidget {
  const NouveauDevisScreen({super.key});

  @override
  State<NouveauDevisScreen> createState() => _NouveauDevisScreenState();
}

class _NouveauDevisScreenState extends State<NouveauDevisScreen> {
  Client? _clientSelectionne;
  final List<DevisItem> _items = [];
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _conditionsController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _remiseController = TextEditingController();
  final TextEditingController _tvaController = TextEditingController(
    text: '20.0',
  );

  DateTime _dateExpiration = DateTime.now().add(const Duration(days: 30));
  bool _remiseEnPourcentage = true;

  @override
  void initState() {
    super.initState();
    _conditionsController.text = 'Devis valable 30 jours. Prix HT en euros.';
  }

  @override
  void dispose() {
    _notesController.dispose();
    _conditionsController.dispose();
    _contactController.dispose();
    _codeController.dispose();
    _remiseController.dispose();
    _tvaController.dispose();
    super.dispose();
  }

  double get _sousTotal {
    return _items.fold(0.0, (total, item) => total + item.sousTotal);
  }

  double get _montantRemise {
    final remise = double.tryParse(_remiseController.text) ?? 0.0;
    if (_remiseEnPourcentage) {
      return _sousTotal * (remise / 100);
    }
    return remise;
  }

  double get _totalHT {
    return _sousTotal - _montantRemise;
  }

  double get _montantTva {
    final taux = double.tryParse(_tvaController.text) ?? 20.0;
    return _totalHT * (taux / 100);
  }

  double get _totalTTC {
    return _totalHT + _montantTva;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nouveau Devis'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          TextButton(
            onPressed:
                _items.isNotEmpty && _clientSelectionne != null
                    ? () => _creerDevis()
                    : null,
            child: const Text('Créer'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Informations client
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informations Client',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Consumer<FirebaseClientProvider>(
                      builder: (context, provider, child) {
                        return DropdownButtonFormField<Client>(
                          value: _clientSelectionne,
                          decoration: const InputDecoration(
                            labelText: 'Client',
                            border: OutlineInputBorder(),
                          ),
                          items:
                              provider.clients.map((client) {
                                return DropdownMenuItem(
                                  value: client,
                                  child: Text(client.nomComplet),
                                );
                              }).toList(),
                          onChanged: (client) {
                            setState(() {
                              _clientSelectionne = client;
                            });
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            // Informations devis
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informations Devis',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ListTile(
                            title: const Text('Date d\'expiration'),
                            subtitle: Text(
                              '${_dateExpiration.day.toString().padLeft(2, '0')}/${_dateExpiration.month.toString().padLeft(2, '0')}/${_dateExpiration.year}',
                            ),
                            trailing: const Icon(Icons.calendar_today),
                            onTap: _selectionnerDateExpiration,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _contactController,
                      decoration: const InputDecoration(
                        labelText: 'Contact commercial (optionnel)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _conditionsController,
                      decoration: const InputDecoration(
                        labelText: 'Conditions de validité',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ),
            // Ajout de produits
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Produits',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    // Champ pour saisir le code produit
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _codeController,
                            decoration: const InputDecoration(
                              hintText: 'Entrer le code produit (ex: SMRT001)',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.qr_code),
                              helperText: 'Appuyez sur Entrée pour ajouter',
                            ),
                            textCapitalization: TextCapitalization.characters,
                            onSubmitted: (code) {
                              _ajouterProduitParCode(code);
                              _codeController.clear();
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: () {
                            if (_codeController.text.isNotEmpty) {
                              _ajouterProduitParCode(_codeController.text);
                              _codeController.clear();
                            }
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Ajouter'),
                        ),
                        const SizedBox(width: 8),
                        OutlinedButton.icon(
                          onPressed: () => _afficherSelectionProduit(),
                          icon: const Icon(Icons.search),
                          label: const Text('Parcourir'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    if (_items.isEmpty)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32),
                          child: Text('Aucun produit ajouté'),
                        ),
                      )
                    else
                      _buildTableauProduits(),
                  ],
                ),
              ),
            ),
            // Remises et TVA
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Remises et TVA',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _remiseController,
                            decoration: InputDecoration(
                              labelText: 'Remise',
                              border: const OutlineInputBorder(),
                              suffixText: _remiseEnPourcentage ? '%' : '€',
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) => setState(() {}),
                          ),
                        ),
                        const SizedBox(width: 8),
                        DropdownButton<bool>(
                          value: _remiseEnPourcentage,
                          items: const [
                            DropdownMenuItem(value: true, child: Text('%')),
                            DropdownMenuItem(value: false, child: Text('€')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _remiseEnPourcentage = value ?? true;
                            });
                          },
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextField(
                            controller: _tvaController,
                            decoration: const InputDecoration(
                              labelText: 'TVA (%)',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) => setState(() {}),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            // Notes
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Notes (optionnel)',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        hintText: 'Ajouter des notes pour ce devis...',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),
            // Totaux
            if (_items.isNotEmpty)
              Container(
                width: double.infinity,
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Sous-total HT:'),
                        Text('${_sousTotal.toStringAsFixed(2)} €'),
                      ],
                    ),
                    if (_montantRemise > 0) ...[
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Remise:'),
                          Text('- ${_montantRemise.toStringAsFixed(2)} €'),
                        ],
                      ),
                    ],
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Total HT:'),
                        Text('${_totalHT.toStringAsFixed(2)} €'),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('TVA (${_tvaController.text}%):'),
                        Text('${_montantTva.toStringAsFixed(2)} €'),
                      ],
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'TOTAL TTC:',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${_totalTTC.toStringAsFixed(2)} €',
                          style: Theme.of(
                            context,
                          ).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 100), // Espace pour le FAB
          ],
        ),
      ),
    );
  }

  Widget _buildTableauProduits() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('Référence')),
          DataColumn(label: Text('Désignation')),
          DataColumn(label: Text('Qté')),
          DataColumn(label: Text('Unité')),
          DataColumn(label: Text('Prix HT')),
          DataColumn(label: Text('Total HT')),
          DataColumn(label: Text('Actions')),
        ],
        rows:
            _items
                .map(
                  (item) => DataRow(
                    cells: [
                      DataCell(Text(item.reference)),
                      DataCell(Text(item.designation)),
                      DataCell(Text(item.quantite.toString())),
                      DataCell(Text(item.unite)),
                      DataCell(Text(item.prixUnitaireFormate)),
                      DataCell(
                        Text(
                          item.sousTotalFormate,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      DataCell(
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit, size: 18),
                              onPressed: () => _modifierQuantite(item),
                              tooltip: 'Modifier quantité',
                            ),
                            IconButton(
                              icon: const Icon(
                                Icons.delete,
                                size: 18,
                                color: Colors.red,
                              ),
                              onPressed: () => _retirerItem(item),
                              tooltip: 'Supprimer',
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
                .toList(),
      ),
    );
  }

  Future<void> _selectionnerDateExpiration() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dateExpiration,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() {
        _dateExpiration = date;
      });
    }
  }

  Future<void> _ajouterProduitParCode(String code) async {
    if (code.trim().isEmpty) return;

    final produit = await context.read<ProduitProvider>().obtenirProduitParCode(
      code.trim(),
    );

    if (produit == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Produit avec le code "$code" introuvable')),
        );
      }
      return;
    }

    _afficherDialogueQuantite(produit);
  }

  void _afficherSelectionProduit() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            expand: false,
            builder: (context, scrollController) {
              return Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Sélectionner un produit',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: Consumer<ProduitProvider>(
                        builder: (context, provider, child) {
                          return ListView.builder(
                            controller: scrollController,
                            itemCount: provider.produits.length,
                            itemBuilder: (context, index) {
                              final produit = provider.produits[index];
                              return ListTile(
                                title: Text(produit.nom),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Code: ${produit.code}'),
                                    Text(
                                      '${produit.prixFormate} - Stock: ${produit.stock}',
                                    ),
                                  ],
                                ),
                                trailing: ElevatedButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    _afficherDialogueQuantite(produit);
                                  },
                                  child: const Text('Ajouter'),
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }

  void _afficherDialogueQuantite(Produit produit) {
    final quantiteController = TextEditingController(text: '1');
    final uniteController = TextEditingController(text: 'pièce');

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Ajouter - ${produit.nom}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Code: ${produit.code}'),
                Text('Prix unitaire: ${produit.prixFormate}'),
                const SizedBox(height: 16),
                TextField(
                  controller: quantiteController,
                  decoration: const InputDecoration(
                    labelText: 'Quantité',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: uniteController,
                  decoration: const InputDecoration(
                    labelText: 'Unité',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  final quantite = int.tryParse(quantiteController.text) ?? 0;
                  if (quantite > 0) {
                    Navigator.pop(context);
                    _ajouterItem(produit, quantite, uniteController.text);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Quantité invalide')),
                    );
                  }
                },
                child: const Text('Ajouter'),
              ),
            ],
          ),
    );
  }

  void _ajouterItem(Produit produit, int quantite, String unite) {
    // Vérifier si le produit est déjà dans la liste
    final index = _items.indexWhere((item) => item.produitId == produit.id);

    if (index != -1) {
      // Mettre à jour la quantité
      final item = _items[index];
      final nouvelleQuantite = item.quantite + quantite;
      _items[index] = DevisItem.fromProduit(
        devisId: 0,
        produitId: produit.id!,
        reference: produit.code,
        designation: produit.nom,
        quantite: nouvelleQuantite,
        unite: unite.isEmpty ? 'pièce' : unite,
        prixUnitaireHT: produit.prix,
      );
    } else {
      // Ajouter un nouvel item
      _items.add(
        DevisItem.fromProduit(
          devisId: 0,
          produitId: produit.id!,
          reference: produit.code,
          designation: produit.nom,
          quantite: quantite,
          unite: unite.isEmpty ? 'pièce' : unite,
          prixUnitaireHT: produit.prix,
        ),
      );
    }

    setState(() {});
  }

  void _modifierQuantite(DevisItem item) {
    final quantiteController = TextEditingController(
      text: item.quantite.toString(),
    );
    final uniteController = TextEditingController(text: item.unite);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Modifier - ${item.designation}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: quantiteController,
                  decoration: const InputDecoration(
                    labelText: 'Quantité',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: uniteController,
                  decoration: const InputDecoration(
                    labelText: 'Unité',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  final quantite = int.tryParse(quantiteController.text) ?? 0;
                  if (quantite > 0) {
                    Navigator.pop(context);
                    final index = _items.indexWhere(
                      (i) => i.produitId == item.produitId,
                    );
                    if (index != -1) {
                      _items[index] = item.copyWith(
                        quantite: quantite,
                        unite:
                            uniteController.text.isEmpty
                                ? 'pièce'
                                : uniteController.text,
                      );
                      setState(() {});
                    }
                  }
                },
                child: const Text('Modifier'),
              ),
            ],
          ),
    );
  }

  void _retirerItem(DevisItem item) {
    setState(() {
      _items.removeWhere((i) => i.produitId == item.produitId);
    });
  }

  Future<void> _creerDevis() async {
    if (_clientSelectionne == null || _items.isEmpty) return;

    final numeroDevis =
        await context.read<DevisProvider>().genererNumeroDevis();
    final remise = double.tryParse(_remiseController.text) ?? 0.0;
    final tva = double.tryParse(_tvaController.text) ?? 20.0;

    final devis = Devis(
      numero: numeroDevis,
      clientId: _clientSelectionne!.id!,
      dateCreation: DateTime.now(),
      dateExpiration: _dateExpiration,
      conditionsValidite: _conditionsController.text.trim(),
      items: _items,
      remisePourcentage: _remiseEnPourcentage ? remise : 0.0,
      remiseMontant: _remiseEnPourcentage ? 0.0 : remise,
      tauxTva: tva,
      notes:
          _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
      contactCommercial:
          _contactController.text.trim().isEmpty
              ? null
              : _contactController.text.trim(),
    );

    final success = await context.read<DevisProvider>().creerDevis(devis);

    if (success && mounted) {
      Navigator.pop(context);
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Devis créé avec succès')));
    }
  }
}
