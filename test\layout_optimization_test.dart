import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import '../lib/screens/home_screen.dart';
import '../lib/screens/merchandising/mes_rapports_screen.dart';
import '../lib/screens/merchandising/creer_rapport_screen.dart';
import '../lib/screens/produits/produits_screen_new.dart';
import '../lib/providers/firebase_client_provider.dart';
import '../lib/providers/produit_provider.dart';
import '../lib/providers/commande_provider.dart';
import '../lib/providers/rapport_provider.dart';

void main() {
  group('Layout Optimization Tests', () {
    late Widget testApp;

    setUp(() {
      testApp = MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => FirebaseClientProvider()),
          ChangeNotifierProvider(create: (_) => ProduitProvider()),
          ChangeNotifierProvider(create: (_) => CommandeProvider()),
          ChangeNotifierProvider(create: (_) => RapportProvider()),
        ],
        child: MaterialApp(
          home: Scaffold(body: Container()),
        ),
      );
    });

    group('Home Screen Layout Tests', () {
      testWidgets('Home screen adapts to different screen sizes', (tester) async {
        // Test small screen (360px)
        await tester.binding.setSurfaceSize(Size(360, 640));
        await tester.pumpWidget(
          MultiProvider(
            providers: [
              ChangeNotifierProvider(create: (_) => FirebaseClientProvider()),
              ChangeNotifierProvider(create: (_) => ProduitProvider()),
              ChangeNotifierProvider(create: (_) => CommandeProvider()),
            ],
            child: MaterialApp(home: HomeScreen()),
          ),
        );
        await tester.pumpAndSettle();

        // Verify statistics cards are in horizontal scroll
        expect(find.byType(ListView), findsWidgets);

        // Test medium screen (600px)
        await tester.binding.setSurfaceSize(Size(600, 800));
        await tester.pumpAndSettle();

        // Test large screen (900px)
        await tester.binding.setSurfaceSize(Size(900, 1200));
        await tester.pumpAndSettle();

        // Verify quick actions are arranged horizontally
        expect(find.byType(Row), findsWidgets);
      });

      testWidgets('Quick actions layout optimizes horizontal space', (tester) async {
        // Test very small screen
        await tester.binding.setSurfaceSize(Size(320, 568));
        await tester.pumpWidget(
          MultiProvider(
            providers: [
              ChangeNotifierProvider(create: (_) => FirebaseClientProvider()),
              ChangeNotifierProvider(create: (_) => ProduitProvider()),
              ChangeNotifierProvider(create: (_) => CommandeProvider()),
            ],
            child: MaterialApp(home: HomeScreen()),
          ),
        );
        await tester.pumpAndSettle();

        // Should show single column layout for very small screens
        expect(find.byType(Column), findsWidgets);

        // Test medium screen - should show 2x2 grid
        await tester.binding.setSurfaceSize(Size(400, 700));
        await tester.pumpAndSettle();

        // Test large screen - should show all actions in one row
        await tester.binding.setSurfaceSize(Size(1000, 800));
        await tester.pumpAndSettle();
      });
    });

    group('Reports Screen Layout Tests', () {
      testWidgets('Reports screen uses horizontal layouts effectively', (tester) async {
        await tester.pumpWidget(
          MultiProvider(
            providers: [
              ChangeNotifierProvider(create: (_) => RapportProvider()),
            ],
            child: MaterialApp(home: MesRapportsScreen()),
          ),
        );
        await tester.pumpAndSettle();

        // Test small screen - should use horizontal scroll for stats
        await tester.binding.setSurfaceSize(Size(400, 600));
        await tester.pumpAndSettle();

        // Verify statistics section exists
        expect(find.byType(Container), findsWidgets);

        // Test large screen - should use horizontal row for stats
        await tester.binding.setSurfaceSize(Size(800, 1000));
        await tester.pumpAndSettle();
      });

      testWidgets('Report cards adapt layout based on screen size', (tester) async {
        await tester.pumpWidget(
          MultiProvider(
            providers: [
              ChangeNotifierProvider(create: (_) => RapportProvider()),
            ],
            child: MaterialApp(home: MesRapportsScreen()),
          ),
        );
        await tester.pumpAndSettle();

        // Test different screen sizes
        final screenSizes = [
          Size(360, 640), // Small
          Size(600, 800), // Medium
          Size(900, 1200), // Large
        ];

        for (final size in screenSizes) {
          await tester.binding.setSurfaceSize(size);
          await tester.pumpAndSettle();
          
          // Verify the screen renders without errors
          expect(find.byType(Scaffold), findsOneWidget);
        }
      });
    });

    group('Form Screen Layout Tests', () {
      testWidgets('Create report form optimizes horizontal space', (tester) async {
        await tester.pumpWidget(
          MultiProvider(
            providers: [
              ChangeNotifierProvider(create: (_) => RapportProvider()),
            ],
            child: MaterialApp(home: CreerRapportScreen()),
          ),
        );
        await tester.pumpAndSettle();

        // Test small screen - should use vertical layout
        await tester.binding.setSurfaceSize(Size(400, 600));
        await tester.pumpAndSettle();

        expect(find.byType(Form), findsOneWidget);

        // Test large screen - should use horizontal layout for date/duration
        await tester.binding.setSurfaceSize(Size(800, 1000));
        await tester.pumpAndSettle();

        // Verify form fields are present
        expect(find.byType(TextFormField), findsWidgets);
      });
    });

    group('Product Screen Layout Tests', () {
      testWidgets('Product grid adapts to screen size optimally', (tester) async {
        await tester.pumpWidget(
          MultiProvider(
            providers: [
              ChangeNotifierProvider(create: (_) => ProduitProvider()),
            ],
            child: MaterialApp(home: ProduitsScreen()),
          ),
        );
        await tester.pumpAndSettle();

        // Test different screen sizes and verify grid adaptation
        final testCases = [
          {'width': 320.0, 'expectedColumns': 1},
          {'width': 600.0, 'expectedColumns': 1},
          {'width': 900.0, 'expectedColumns': 2},
          {'width': 1200.0, 'expectedColumns': 3},
          {'width': 1600.0, 'expectedColumns': 4},
        ];

        for (final testCase in testCases) {
          await tester.binding.setSurfaceSize(
            Size(testCase['width'] as double, 800),
          );
          await tester.pumpAndSettle();

          // Verify the screen renders
          expect(find.byType(Scaffold), findsOneWidget);
        }
      });
    });

    group('Responsive Breakpoints Tests', () {
      testWidgets('All screens handle responsive breakpoints correctly', (tester) async {
        final screens = [
          HomeScreen(),
          MesRapportsScreen(),
          CreerRapportScreen(),
          ProduitsScreen(),
        ];

        final breakpoints = [
          Size(320, 568),  // iPhone SE
          Size(360, 640),  // Small Android
          Size(414, 896),  // iPhone 11 Pro Max
          Size(600, 800),  // Small tablet
          Size(768, 1024), // iPad
          Size(1024, 768), // iPad landscape
          Size(1200, 800), // Desktop
          Size(1920, 1080), // Large desktop
        ];

        for (final screen in screens) {
          for (final size in breakpoints) {
            await tester.binding.setSurfaceSize(size);
            
            await tester.pumpWidget(
              MultiProvider(
                providers: [
                  ChangeNotifierProvider(create: (_) => FirebaseClientProvider()),
                  ChangeNotifierProvider(create: (_) => ProduitProvider()),
                  ChangeNotifierProvider(create: (_) => CommandeProvider()),
                  ChangeNotifierProvider(create: (_) => RapportProvider()),
                ],
                child: MaterialApp(home: screen),
              ),
            );
            
            await tester.pumpAndSettle();
            
            // Verify no overflow errors
            expect(tester.takeException(), isNull);
            
            // Verify basic structure is present
            expect(find.byType(Scaffold), findsOneWidget);
          }
        }
      });
    });
  });
}
